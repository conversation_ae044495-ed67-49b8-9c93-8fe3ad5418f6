//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.flamesmplugin;

import java.util.Arrays;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.serializer.legacy.LegacyComponentSerializer;
import org.bukkit.Material;
import org.bukkit.NamespacedKey;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.ShapedRecipe;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataType;
import org.bukkit.plugin.java.JavaPlugin;

public class FlameItems {
    private final NamespacedKey customItemKey;
    private final NamespacedKey flameMatchKey;
    private final NamespacedKey flameMaceKey;
    private final NamespacedKey traderTokenKey;
    private final NamespacedKey flameShieldKey;
    private final NamespacedKey flameLifeKey;

    public FlameItems(FlameSMPlugin plugin) {
        this.customItemKey = new NamespacedKey(plugin, "custom_item");
        this.flameMatchKey = new NamespacedKey(plugin, "flame_match");
        this.flameMaceKey = new NamespacedKey(plugin, "flame_mace");
        this.traderTokenKey = new NamespacedKey(plugin, "trader_token");
        this.flameShieldKey = new NamespacedKey(plugin, "flame_shield");
        this.flameLifeKey = new NamespacedKey(plugin, "flame_life");
    }

    public ItemStack createFlameShield() {
        ItemStack shield = new ItemStack(Material.SHIELD);
        ItemMeta meta = shield.getItemMeta();
        if (meta != null) {
            meta.displayName(LegacyComponentSerializer.legacySection().deserialize("§6§lFlame Shield"));
            meta.lore(Arrays.asList(
                LegacyComponentSerializer.legacySection().deserialize("§7Unbreakable. Cannot be disabled."),
                LegacyComponentSerializer.legacySection().deserialize("§7Grants permanent Fire Resistance."),
                Component.empty(),
                LegacyComponentSerializer.legacySection().deserialize("§e§lSneak + Right Click: Resistance IV, Speed II (50s)")
            ));
            meta.setUnbreakable(true);
            meta.getPersistentDataContainer().set(this.flameShieldKey, PersistentDataType.BOOLEAN, true);
            meta.getPersistentDataContainer().set(this.customItemKey, PersistentDataType.STRING, "flame_shield");
            shield.setItemMeta(meta);
        }
        return shield;
    }

    public boolean isFlameShield(ItemStack item) {
        return item != null && item.getItemMeta() != null && item.getItemMeta().getPersistentDataContainer().has(this.flameShieldKey, PersistentDataType.BOOLEAN);
    }

    public ItemStack createFlameMatch() {
        ItemStack match = new ItemStack(Material.TORCH);
        ItemMeta meta = match.getItemMeta();
        if (meta != null) {
            meta.displayName(LegacyComponentSerializer.legacySection().deserialize("§6§lFlame Match"));
            meta.lore(Arrays.asList(LegacyComponentSerializer.legacySection().deserialize("§7A mystical match that can relight"), LegacyComponentSerializer.legacySection().deserialize("§7an extinguished flame."), Component.empty(), LegacyComponentSerializer.legacySection().deserialize("§e§lRight-click to use")));
            meta.getPersistentDataContainer().set(this.customItemKey, PersistentDataType.STRING, "flame_match");
            meta.getPersistentDataContainer().set(this.flameMatchKey, PersistentDataType.BOOLEAN, true);
            meta.addEnchant(Enchantment.UNBREAKING, 1, true);
            match.setItemMeta(meta);
        }

        return match;
    }

    public ItemStack createFlameMace() {
        ItemStack mace = new ItemStack(Material.MACE);
        ItemMeta meta = mace.getItemMeta();
        if (meta != null) {
            meta.displayName(LegacyComponentSerializer.legacySection().deserialize("§c§lFlame Mace"));
            meta.lore(Arrays.asList(
                LegacyComponentSerializer.legacySection().deserialize("§7A legendary weapon forged in the"),
                LegacyComponentSerializer.legacySection().deserialize("§7flames of ancient dragons."),
                Component.empty(),
                LegacyComponentSerializer.legacySection().deserialize("§c§lSpecial Abilities:"),
                LegacyComponentSerializer.legacySection().deserialize("§7• Dash ability"),
                LegacyComponentSerializer.legacySection().deserialize("§7• Breaks all shields within 30 blocks"),
                LegacyComponentSerializer.legacySection().deserialize("§b• Wind Burst I"),
                LegacyComponentSerializer.legacySection().deserialize("§8• Density V"),
                Component.empty(),
                LegacyComponentSerializer.legacySection().deserialize("§e§lRight-click to dash")
            ));
            meta.getPersistentDataContainer().set(this.customItemKey, PersistentDataType.STRING, "flame_mace");
            meta.getPersistentDataContainer().set(this.flameMaceKey, PersistentDataType.BOOLEAN, true);
            meta.addEnchant(Enchantment.SHARPNESS, 5, true);
            meta.addEnchant(Enchantment.UNBREAKING, 3, true);
            meta.addEnchant(Enchantment.FIRE_ASPECT, 2, true);
            // Custom enchantments for Wind Burst I and Density V
            try {
                Enchantment windBurst = Enchantment.getByKey(NamespacedKey.minecraft("wind_burst"));
                if (windBurst != null) meta.addEnchant(windBurst, 1, true);
            } catch (Exception ignored) {}
            try {
                Enchantment density = Enchantment.getByKey(NamespacedKey.minecraft("density"));
                if (density != null) meta.addEnchant(density, 5, true);
            } catch (Exception ignored) {}
            mace.setItemMeta(meta);
        }

        return mace;
    }

    public ItemStack createFlameUpgrader() {
        ItemStack upgrader = new ItemStack(Material.BLAZE_POWDER);
        ItemMeta meta = upgrader.getItemMeta();
        if (meta != null) {
            meta.displayName(LegacyComponentSerializer.legacySection().deserialize("§a§lFlame Upgrader"));
            meta.lore(Arrays.asList(LegacyComponentSerializer.legacySection().deserialize("§7Upgrades your flame to Level 2,"), LegacyComponentSerializer.legacySection().deserialize("§7unlocking powerful new abilities."), Component.empty(), LegacyComponentSerializer.legacySection().deserialize("§e§lRight-click to use")));
            meta.getPersistentDataContainer().set(this.customItemKey, PersistentDataType.STRING, "flame_upgrader");
            meta.addEnchant(Enchantment.UNBREAKING, 1, true);
            upgrader.setItemMeta(meta);
        }

        return upgrader;
    }

    public ItemStack createFlameLife() {
        ItemStack flameLife = new ItemStack(Material.TOTEM_OF_UNDYING);
        ItemMeta meta = flameLife.getItemMeta();
        if (meta != null) {
            meta.displayName(LegacyComponentSerializer.legacySection().deserialize("§d§lFlame Life"));
            meta.lore(Arrays.asList(
                LegacyComponentSerializer.legacySection().deserialize("§7A precious essence of flame energy"),
                LegacyComponentSerializer.legacySection().deserialize("§7that can restore your flame's health."),
                Component.empty(),
                LegacyComponentSerializer.legacySection().deserialize("§7Restores 1 flame health when used"),
                Component.empty(),
                LegacyComponentSerializer.legacySection().deserialize("§e§lRight-click to use")
            ));
            meta.getPersistentDataContainer().set(flameLifeKey, PersistentDataType.BOOLEAN, true);
            flameLife.setItemMeta(meta);
        }
        return flameLife;
    }

    public boolean isFlameLife(ItemStack item) {
        return item != null && item.getItemMeta() != null &&
               item.getItemMeta().getPersistentDataContainer().has(flameLifeKey, PersistentDataType.BOOLEAN);
    }

    public ItemStack createTraderToken() {
        ItemStack token = new ItemStack(Material.NETHER_STAR);
        ItemMeta meta = token.getItemMeta();
        if (meta != null) {
            meta.displayName(LegacyComponentSerializer.legacySection().deserialize("§b§lFlame Trader Token"));
            meta.lore(Arrays.asList(LegacyComponentSerializer.legacySection().deserialize("§7Use this to trade your flame for another!"), Component.empty(), LegacyComponentSerializer.legacySection().deserialize("§eRight-click to open the trader.")));
            meta.getPersistentDataContainer().set(this.traderTokenKey, PersistentDataType.BYTE, (byte)1);
            meta.addEnchant(Enchantment.UNBREAKING, 1, true);
            token.setItemMeta(meta);
        }

        return token;
    }

    public void registerTraderTokenRecipe(FlameSMPlugin plugin) {
        ItemStack result = this.createTraderToken();
        ShapedRecipe recipe = new ShapedRecipe(this.traderTokenKey, result);
        recipe.shape(new String[]{"DBD", "NSN", "DGD"});
        recipe.setIngredient('D', Material.DIAMOND_BLOCK);
        recipe.setIngredient('B', Material.TOTEM_OF_UNDYING);
        recipe.setIngredient('N', Material.NETHERITE_SCRAP);
        recipe.setIngredient('S', Material.NETHER_STAR);
        recipe.setIngredient('G', Material.ENCHANTED_GOLDEN_APPLE);
        plugin.getServer().addRecipe(recipe);
    }

    public void registerFlameLifeRecipe(JavaPlugin plugin) {
        ItemStack flameLife = this.createFlameLife();
        NamespacedKey key = new NamespacedKey(plugin, "flame_life");
        ShapedRecipe recipe = new ShapedRecipe(key, flameLife);
        recipe.shape(new String[]{"DHD", "HTH", "DHD"});
        recipe.setIngredient('D', Material.DIAMOND);
        recipe.setIngredient('H', Material.GOLDEN_APPLE);
        recipe.setIngredient('T', Material.TOTEM_OF_UNDYING);
        plugin.getServer().addRecipe(recipe);
    }

    public void registerMatchRecipe(JavaPlugin plugin) {
        ItemStack match = this.createFlameMatch();
        NamespacedKey key = new NamespacedKey(plugin, "match");
        ShapedRecipe recipe = new ShapedRecipe(key, match);
        recipe.shape(new String[]{"MCM", "SNS", "MCM"});
        recipe.setIngredient('M', Material.MAGMA_BLOCK);
        recipe.setIngredient('C', Material.END_CRYSTAL);
        recipe.setIngredient('S', Material.SOUL_CAMPFIRE);
        recipe.setIngredient('N', Material.SNIFFER_EGG);
        plugin.getServer().addRecipe(recipe);
    }

    public boolean isFlameMatch(ItemStack item) {
        return item != null && item.getItemMeta() != null ? item.getItemMeta().getPersistentDataContainer().has(this.flameMatchKey, PersistentDataType.BOOLEAN) : false;
    }

    public boolean isFlameMace(ItemStack item) {
        return item != null && item.getItemMeta() != null ? item.getItemMeta().getPersistentDataContainer().has(this.flameMaceKey, PersistentDataType.BOOLEAN) : false;
    }

    public boolean isFlameUpgrader(ItemStack item) {
        if (item != null && item.getItemMeta() != null) {
            String customType = (String)item.getItemMeta().getPersistentDataContainer().get(this.customItemKey, PersistentDataType.STRING);
            return "flame_upgrader".equals(customType);
        } else {
            return false;
        }
    }

    public boolean isTraderToken(ItemStack item) {
        if (item != null && item.hasItemMeta()) {
            ItemMeta meta = item.getItemMeta();
            return meta != null && meta.getPersistentDataContainer().has(this.traderTokenKey, PersistentDataType.BYTE);
        } else {
            return false;
        }
    }

    public String getCustomItemType(ItemStack item) {
        return item != null && item.getItemMeta() != null ? (String)item.getItemMeta().getPersistentDataContainer().get(this.customItemKey, PersistentDataType.STRING) : null;
    }

    public void autoEnchantTool(ItemStack item, FlameType flameType) {
        if (item != null && item.getItemMeta() != null && flameType != null) {
            ItemMeta meta = item.getItemMeta();
            boolean modified = false;
            switch (flameType) {
                case EARTH:
                    if (this.isPickaxe(item)) {
                        meta.addEnchant(Enchantment.EFFICIENCY, 2, true);
                        modified = true;
                    }
                    break;
                case AQUATIC:
                    if (this.isHelmet(item)) {
                        meta.addEnchant(Enchantment.AQUA_AFFINITY, 1, true);
                        modified = true;
                    }
                    break;
                case COSMIC:
                    if (this.isArmor(item)) {
                        // Always add or upgrade to at least Protection I
                        int currentLevel = meta.hasEnchant(Enchantment.PROTECTION) ? meta.getEnchantLevel(Enchantment.PROTECTION) : 0;
                        if (currentLevel < 1) {
                            meta.addEnchant(Enchantment.PROTECTION, 1, true);
                            modified = true;
                        }
                    }
                    break;
                case DRAGON:
                    // No auto-enchant for DRAGON
                    break;
                case FROST:
                    // No auto-enchant for FROST
                    break;
                case LIFE:
                    // No auto-enchant for LIFE
                    break;
                case SHADOW:
                    // No auto-enchant for SHADOW
                    break;
                case GUST:
                    // No auto-enchant for GUST
                    break;
                case BURNING:
                    // No auto-enchant for BURNING
                    break;
            }

            if (modified) {
                item.setItemMeta(meta);
            }
        }
    }

    private boolean isPickaxe(ItemStack item) {
        Material type = item.getType();
        return type == Material.WOODEN_PICKAXE || type == Material.STONE_PICKAXE || type == Material.IRON_PICKAXE || type == Material.GOLDEN_PICKAXE || type == Material.DIAMOND_PICKAXE || type == Material.NETHERITE_PICKAXE;
    }

    public boolean isHelmet(ItemStack item) {
        Material type = item.getType();
        return type == Material.LEATHER_HELMET || type == Material.CHAINMAIL_HELMET || type == Material.IRON_HELMET || type == Material.GOLDEN_HELMET || type == Material.DIAMOND_HELMET || type == Material.NETHERITE_HELMET || type == Material.TURTLE_HELMET;
    }

    private boolean isArmor(ItemStack item) {
        String typeName = item.getType().name();
        return typeName.contains("HELMET") || typeName.contains("CHESTPLATE") || typeName.contains("LEGGINGS") || typeName.contains("BOOTS");
    }

    // Create a flame item by name (for SilentOps integration)
    public ItemStack createFlameItem(String name) {
        String key = name.toLowerCase();
        switch (key) {
            case "shield":
            case "flameshield":
                return createFlameShield();
            case "match":
            case "flamematch":
                return createFlameMatch();
            case "mace":
            case "flamemace":
                return createFlameMace();
            case "life":
            case "flamelife":
                return createFlameLife();
            case "upgrader":
            case "flameupgrader":
                return createFlameUpgrader();
            // Add more custom items as needed
            default:
                // Fallback: try to create a vanilla item
                org.bukkit.Material mat = org.bukkit.Material.matchMaterial(name.toUpperCase());
                if (mat != null) return new org.bukkit.inventory.ItemStack(mat, 1);
                return new org.bukkit.inventory.ItemStack(org.bukkit.Material.STICK, 1); // fallback
        }
    }
}
