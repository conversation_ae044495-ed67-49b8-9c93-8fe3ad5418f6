package org.flamesmplugin.commands;

import org.bukkit.command.Command;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class SetAbilityDamageTabCompleter implements TabCompleter {
    private static final List<String> ABILITIES = Arrays.asList(
            "all", "burning", "frost", "earth", "aquatic", "gust", "cosmic", "shadow", "life", "dragon"
    );

    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        if (args.length == 1) {
            List<String> suggestions = new ArrayList<>();
            for (String ability : ABILITIES) {
                if (ability.toLowerCase().startsWith(args[0].toLowerCase())) {
                    suggestions.add(ability);
                }
            }
            return suggestions;
        }
        if (args.length == 2) {
            return Collections.singletonList("<amount>");
        }
        return Collections.emptyList();
    }
}
