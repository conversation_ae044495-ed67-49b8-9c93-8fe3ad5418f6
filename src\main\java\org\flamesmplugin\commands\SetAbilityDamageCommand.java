//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

package org.flamesmplugin.commands;

import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.flamesmplugin.FlameAbilities;

public class SetAbilityDamageCommand implements CommandExecutor {
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!sender.hasPermission("flamesm.setabilitydamage")) {
            sender.sendMessage("§cYou do not have permission to use this command.");
            return true;
        }

        // Usage: /setabilitydamage <ability|all> <amount>
        if (args.length == 2) {
            String ability = args[0];
            try {
                double value = Double.parseDouble(args[1]);
                if (ability.equalsIgnoreCase("all")) {
                    FlameAbilities.setAllAbilityDamage(value);
                    sender.sendMessage("§aAll ability damage set to: " + value);
                } else {
                    FlameAbilities.setAbilityDamage(ability, value);
                    sender.sendMessage("§a" + ability.substring(0, 1).toUpperCase() + ability.substring(1).toLowerCase() + " ability damage set to: " + value);
                }
            } catch (NumberFormatException e) {
                sender.sendMessage("§cInvalid number: " + args[1]);
            }
            return true;
        }

        // If only one argument, show usage (do not try to parse as value)
        sender.sendMessage("§eUsage: /setabilitydamage <ability|all> <amount>");
        return true;
    }
}
