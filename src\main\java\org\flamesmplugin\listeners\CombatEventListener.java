//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.flamesmplugin.listeners;

import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Monster;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityTargetEvent;
import org.flamesmplugin.FlameAbilities;
import org.flamesmplugin.FlameManager;
import org.flamesmplugin.FlamePlayer;
import org.flamesmplugin.FlameType;

public class CombatEventListener implements Listener {
    private final FlameManager flameManager;
    private final FlameAbilities flameAbilities;

    public CombatEventListener(FlameManager flameManager, FlameAbilities flameAbilities) {
        this.flameManager = flameManager;
        this.flameAbilities = flameAbilities;
    }

    @EventHandler
    public void onEntityDamageByEntity(EntityDamageByEntityEvent event) {
        if (event.getDamager() instanceof Player) {
            Player attacker = (Player)event.getDamager();
            if (event.getEntity() instanceof LivingEntity) {
                LivingEntity target = (LivingEntity)event.getEntity();
                this.flameAbilities.handleCombatEffect(attacker, target, event.getDamage());
            }
        }

    }

    @EventHandler
    public void onEntityTarget(EntityTargetEvent event) {
        if (event.getEntity() instanceof Monster && event.getTarget() instanceof Player) {
            Player player = (Player)event.getTarget();
            FlamePlayer flamePlayer = this.flameManager.getFlamePlayer(player);
            if (flamePlayer.hasActiveFlame() && flamePlayer.getFlameType() == FlameType.DRAGON) {
                event.setCancelled(true);
            }
        }

    }
}
