//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.flamesmplugin;

import java.time.Duration;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.TextComponent;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextColor;
import net.kyori.adventure.text.format.TextDecoration;
import net.kyori.adventure.title.Title;
import net.kyori.adventure.title.Title.Times;
import org.bukkit.entity.Player;

public class MessageUtils {
    public static void sendActionBar(Player player, String message, TextColor color) {
        Component component = Component.text(message).color(color);
        player.sendActionBar(component);
    }

    public static void sendActionBar(Player player, String message) {
        sendActionBar(player, message, NamedTextColor.WHITE);
    }

    public static void sendTitle(Player player, String title, String subtitle, TextColor titleColor, TextColor subtitleColor) {
        Component titleComponent = ((TextComponent)Component.text(title).color(titleColor)).decoration(TextDecoration.BOLD, true);
        Component subtitleComponent = (Component)(subtitle != null ? Component.text(subtitle).color(subtitleColor) : Component.empty());
        Title titleMessage = Title.title(titleComponent, subtitleComponent, Times.times(Duration.ofMillis(500L), Duration.ofSeconds(2L), Duration.ofMillis(500L)));
        player.showTitle(titleMessage);
    }

    public static void sendTitle(Player player, String title, String subtitle) {
        sendTitle(player, title, subtitle, NamedTextColor.WHITE, NamedTextColor.GRAY);
    }

    public static void sendAbilityActivation(Player player, String abilityName, TextColor color) {
        sendActionBar(player, abilityName + " activated", color);
    }

    public static void sendCooldownMessage(Player player, int seconds) {
        sendActionBar(player, "Ability on cooldown (" + seconds + "s)", NamedTextColor.RED);
    }

    public static void sendUpgradeRequired(Player player) {
        sendActionBar(player, "Level 2 upgrade required", NamedTextColor.RED);
    }

    public static void sendFlameStatus(Player player, String message, TextColor color) {
        sendTitle(player, message, (String)null, color, (TextColor)null);
    }

    public static void sendEffectNotification(Player player, String effect, TextColor color) {
        sendActionBar(player, effect, color);
    }

    public static void sendBonusDamage(Player player, String bonusType) {
        sendActionBar(player, bonusType + " bonus damage", NamedTextColor.GOLD);
    }
}
