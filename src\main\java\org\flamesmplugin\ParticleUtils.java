//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.flamesmplugin;

import org.bukkit.Bukkit;
import org.bukkit.Color;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.World;
import org.bukkit.block.data.BlockData;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.Vector;

public class ParticleUtils {
    // Pre-attack warning: beam (for Sonic Boom)
    public static void showPreAttackBeam(final Location center, final float yaw, final double length, final double width, final int durationTicks, final Player player) {
        final World world = center.getWorld();
        if (world != null) {
            final double y = center.getY() + 1.5;
            final Particle.DustOptions red = new Particle.DustOptions(Color.fromRGB(255, 40, 40), 1.5F);
            final Vector dir = new Vector(-Math.sin(Math.toRadians(yaw)), 0, Math.cos(Math.toRadians(yaw)));
            (new BukkitRunnable() {
                int ticks = 0;
                public void run() {
                    if (this.ticks++ > durationTicks) {
                        this.cancel();
                    } else {
                        for (double d = 0; d < length; d += 0.5) {
                            Location base = center.clone().add(dir.clone().multiply(d));
                            base.setY(y);
                            for (double w = -width/2; w <= width/2; w += 0.3) {
                                Vector side = new Vector(-dir.getZ(), 0, dir.getX()).normalize().multiply(w);
                                Location p = base.clone().add(side);
                                if (player != null) {
                                    player.spawnParticle(Particle.DUST, p, 0, 0, 0, 0, 0, red);
                                } else {
                                    world.spawnParticle(Particle.DUST, p, 0, 0, 0, 0, 0, red);
                                }
                            }
                        }
                    }
                }
            }).runTaskTimer(Bukkit.getPluginManager().getPlugin("FlameSMPlugin"), 0L, 1L);
        }
    }

    // Pre-attack warning: filled circle (for ground slam, darkness pulse, meteor rain)
    public static void showPreAttackCircle(final Location center, final double radius, final int durationTicks, final Player player) {
        final World world = center.getWorld();
        if (world != null) {
            final double y = center.getY() + 0.1;
            final Particle.DustOptions red = new Particle.DustOptions(Color.fromRGB(255, 40, 40), 1.5F);
            (new BukkitRunnable() {
                int ticks = 0;
                public void run() {
                    if (this.ticks++ > durationTicks) {
                        this.cancel();
                    } else {
                        for (double r = 0; r <= radius; r += 0.3) {
                            int points = (int)(2 * Math.PI * r / 0.3);
                            for (int i = 0; i < points; i++) {
                                double angle = 2 * Math.PI * i / points;
                                double x = center.getX() + r * Math.cos(angle);
                                double z = center.getZ() + r * Math.sin(angle);
                                Location p = new Location(world, x, y, z);
                                if (player != null) {
                                    player.spawnParticle(Particle.DUST, p, 0, 0, 0, 0, 0, red);
                                } else {
                                    world.spawnParticle(Particle.DUST, p, 0, 0, 0, 0, 0, red);
                                }
                            }
                        }
                    }
                }
            }).runTaskTimer(Bukkit.getPluginManager().getPlugin("FlameSMPlugin"), 0L, 1L);
        }
    }

    // Pre-attack warning: ring (for shockwave)
    public static void showPreAttackRing(final Location center, final double outerRadius, final double thickness, final int durationTicks, final Player player) {
        final World world = center.getWorld();
        if (world != null) {
            final double y = center.getY() + 0.1;
            final Particle.DustOptions red = new Particle.DustOptions(Color.fromRGB(255, 40, 40), 1.5F);
            (new BukkitRunnable() {
                int ticks = 0;
                public void run() {
                    if (this.ticks++ > durationTicks) {
                        this.cancel();
                    } else {
                        for (double r = outerRadius - thickness; r <= outerRadius; r += 0.2) {
                            int points = (int)(2 * Math.PI * r / 0.3);
                            for (int i = 0; i < points; i++) {
                                double angle = 2 * Math.PI * i / points;
                                double x = center.getX() + r * Math.cos(angle);
                                double z = center.getZ() + r * Math.sin(angle);
                                Location p = new Location(world, x, y, z);
                                if (player != null) {
                                    player.spawnParticle(Particle.DUST, p, 0, 0, 0, 0, 0, red);
                                } else {
                                    world.spawnParticle(Particle.DUST, p, 0, 0, 0, 0, 0, red);
                                }
                            }
                        }
                    }
                }
            }).runTaskTimer(Bukkit.getPluginManager().getPlugin("FlameSMPlugin"), 0L, 1L);
        }
    }
    public static void showPreAttackBox(final Location center, double width, double length, final int durationTicks, final Player player) {
        final World world = center.getWorld();
        if (world != null) {
            final double y = center.getY() + 0.05;
            final double halfW = width / 2.0;
            final double halfL = length / 2.0;
            final Particle.DustOptions red = new Particle.DustOptions(Color.fromRGB(255, 40, 40), 1.5F);
            final float yaw = center.getYaw();
            (new BukkitRunnable() {
                int ticks = 0;
                public void run() {
                    if (this.ticks++ > durationTicks) {
                        this.cancel();
                    } else {
                        // Draw a rotated rectangle (box) based on yaw
                        for (double x = -halfW; x <= halfW; x += 0.4) {
                            for (double z : new double[]{-halfL, halfL}) {
                                double[] rot = ParticleUtils.rotateXZ(x, z, yaw);
                                Location p = center.clone().add(rot[0], y - center.getY(), rot[1]);
                                if (player != null) {
                                    player.spawnParticle(Particle.DUST, p, 0, 0, 0, 0, 0, red);
                                } else {
                                    world.spawnParticle(Particle.DUST, p, 0, 0, 0, 0, 0, red);
                                }
                            }
                        }
                        for (double z = -halfL; z <= halfL; z += 0.4) {
                            for (double x : new double[]{-halfW, halfW}) {
                                double[] rot = ParticleUtils.rotateXZ(x, z, yaw);
                                Location p = center.clone().add(rot[0], y - center.getY(), rot[1]);
                                if (player != null) {
                                    player.spawnParticle(Particle.DUST, p, 0, 0, 0, 0, 0, red);
                                } else {
                                    world.spawnParticle(Particle.DUST, p, 0, 0, 0, 0, 0, red);
                                }
                            }
                        }
                    }
                }
            }).runTaskTimer(Bukkit.getPluginManager().getPlugin("FlameSMPlugin"), 0L, 1L);
        }
    }

    // Helper to rotate a point (x, z) by yaw (in degrees)
    private static double[] rotateXZ(double x, double z, float yaw) {
        double rad = Math.toRadians(-yaw);
        double cos = Math.cos(rad);
        double sin = Math.sin(rad);
        double rx = x * cos - z * sin;
        double rz = x * sin + z * cos;
        return new double[]{rx, rz};
    }

    public static void createCircle(Location center, Particle particle, double radius, int points) {
        World world = center.getWorld();
        if (world != null) {
            for(int i = 0; i < points; ++i) {
                double angle = (Math.PI * 2D) * (double)i / (double)points;
                double x = center.getX() + radius * Math.cos(angle);
                double z = center.getZ() + radius * Math.sin(angle);
                Location particleLoc = new Location(world, x, center.getY(), z);
                world.spawnParticle(particle, particleLoc, 1, (double)0.0F, (double)0.0F, (double)0.0F, (double)0.0F);
            }

        }
    }

    public static void createCircle(Location center, Particle particle, double radius, int points, Material material) {
        World world = center.getWorld();
        if (world != null) {
            BlockData blockData = material.createBlockData();

            for(int i = 0; i < points; ++i) {
                double angle = (Math.PI * 2D) * (double)i / (double)points;
                double x = center.getX() + radius * Math.cos(angle);
                double z = center.getZ() + radius * Math.sin(angle);
                Location particleLoc = new Location(world, x, center.getY(), z);
                world.spawnParticle(particle, particleLoc, 1, (double)0.0F, (double)0.0F, (double)0.0F, (double)0.0F, blockData);
            }

        }
    }

    public static void createSpiral(Location center, Particle particle, double height, double radius) {
        World world = center.getWorld();
        if (world != null) {
            for(double y = (double)0.0F; y <= height; y += 0.2) {
                double angle = y * (double)4.0F;
                double x = center.getX() + radius * Math.cos(angle);
                double z = center.getZ() + radius * Math.sin(angle);
                Location particleLoc = new Location(world, x, center.getY() + y, z);
                world.spawnParticle(particle, particleLoc, 1, (double)0.0F, (double)0.0F, (double)0.0F, (double)0.0F);
            }

        }
    }

    public static void createLine(Location start, Location end, Particle particle, double spacing) {
        World world = start.getWorld();
        if (world != null && world.equals(end.getWorld())) {
            Vector direction = end.toVector().subtract(start.toVector());
            double distance = direction.length();
            direction.normalize();
            boolean isBlockParticle = (particle == Particle.BLOCK || particle == Particle.FALLING_DUST);
            org.bukkit.block.data.BlockData blockData = null;
            if (isBlockParticle) {
                blockData = org.bukkit.Material.DIRT.createBlockData(); // Default to DIRT if not specified
            }
            for(double d = (double)0.0F; d <= distance; d += spacing) {
                Location particleLoc = start.clone().add(direction.clone().multiply(d));
                if (isBlockParticle) {
                    world.spawnParticle(particle, particleLoc, 1, 0.0, 0.0, 0.0, 0.0, blockData);
                } else {
                    world.spawnParticle(particle, particleLoc, 1, 0.0, 0.0, 0.0, 0.0);
                }
            }
        }
    }

    public static void createExplosion(Location center, Particle particle, int particleCount) {
        World world = center.getWorld();
        if (world != null) {
            for(int i = 0; i < particleCount; ++i) {
                Vector randomDirection = (new Vector(Math.random() - 0.5, Math.random() - 0.5, Math.random() - 0.5)).normalize().multiply(Math.random() * 2.0);
                Location particleLoc = center.clone().add(randomDirection);
                world.spawnParticle(particle, particleLoc, 1, 0.0, 0.0, 0.0, 0.0);
            }
            // 40% more damage to nearby entities if this is a flame explosion
            if (particle == org.bukkit.Particle.FLAME) {
                double radius = 3.5; // reasonable explosion radius
                double baseDamage = 8.0; // typical explosion damage
                double extra = baseDamage * 0.4;
                double totalDamage = baseDamage + extra;
                for (org.bukkit.entity.Entity entity : world.getNearbyEntities(center, radius, radius, radius)) {
                    if (entity instanceof org.bukkit.entity.LivingEntity living && !(living instanceof org.bukkit.entity.Player)) {
                        living.damage(totalDamage);
                    }
                }
            }
        }
    }

    public static void createWave(Location start, Vector direction, Particle particle, double length, double width) {
        World world = start.getWorld();
        if (world != null) {
            Vector perpendicular = (new Vector(-direction.getZ(), (double)0.0F, direction.getX())).normalize();

            for(double d = (double)0.0F; d <= length; d += (double)0.5F) {
                for(double w = -width / (double)2.0F; w <= width / (double)2.0F; w += 0.3) {
                    Location particleLoc = start.clone().add(direction.clone().multiply(d)).add(perpendicular.clone().multiply(w));
                    world.spawnParticle(particle, particleLoc, 1, (double)0.0F, (double)0.0F, (double)0.0F, (double)0.0F);
                }
            }

        }
    }

    public static void createStar(Location center, Particle particle, double size) {
        createStar(center, particle, size, null);
    }

    public static void createStar(Location center, Particle particle, double size, org.bukkit.block.data.BlockData blockData) {
        World world = center.getWorld();
        if (world != null) {
            boolean isBlockParticle = (particle == Particle.BLOCK || particle == Particle.FALLING_DUST);
            for(int i = 0; i < 5; ++i) {
                double angle1 = Math.toRadians((double)(i * 72));
                double angle2 = Math.toRadians((double)((i + 1) * 72));
                Location point1 = center.clone().add(size * Math.cos(angle1), 0.0, size * Math.sin(angle1));
                Location point2 = center.clone().add(size * Math.cos(angle2), 0.0, size * Math.sin(angle2));
                double innerAngle = Math.toRadians((double)(i * 72 + 36));
                Location innerPoint = center.clone().add(size * 0.4 * Math.cos(innerAngle), 0.0, size * 0.4 * Math.sin(innerAngle));
                if (isBlockParticle && blockData != null) {
                    createLine(point1, innerPoint, particle, 0.2, blockData);
                    createLine(innerPoint, point2, particle, 0.2, blockData);
                } else {
                    createLine(point1, innerPoint, particle, 0.2);
                    createLine(innerPoint, point2, particle, 0.2);
                }
            }
        }
    }

    // Overload for blockData
    public static void createLine(Location start, Location end, Particle particle, double spacing, org.bukkit.block.data.BlockData blockData) {
        World world = start.getWorld();
        if (world != null && world.equals(end.getWorld())) {
            Vector direction = end.toVector().subtract(start.toVector());
            double distance = direction.length();
            direction.normalize();
            for(double d = (double)0.0F; d <= distance; d += spacing) {
                Location particleLoc = start.clone().add(direction.clone().multiply(d));
                world.spawnParticle(particle, particleLoc, 1, 0.0, 0.0, 0.0, 0.0, blockData);
            }
        }
    }

    public static void createAmbientEffect(Player player, FlameType flameType) {
        if (flameType != null) {
            Location loc = player.getLocation().add((double)0.0F, (double)1.0F, (double)0.0F);
            Particle particle = flameType.getParticle();
            World world = loc.getWorld();
            if (world != null) {
                for(int i = 0; i < 3; ++i) {
                    Vector offset = new Vector((Math.random() - (double)0.5F) * (double)2.0F, Math.random() * (double)2.0F, (Math.random() - (double)0.5F) * (double)2.0F);
                    world.spawnParticle(particle, loc.clone().add(offset), 1, (double)0.0F, (double)0.0F, (double)0.0F, (double)0.0F);
                }

            }
        }
    }

    public static void createIceExplosion(Location center, int shardCount) {
        final World world = center.getWorld();
        if (world != null) {
            for(int i = 0; i < shardCount; ++i) {
                double angle = (Math.PI * 2D) * (double)i / (double)shardCount;
                double x = Math.cos(angle);
                double z = Math.sin(angle);
                final Vector dir = (new Vector(x, 0.2 + Math.random() * (double)0.5F, z)).normalize().multiply(0.7 + Math.random() * 0.7);
                final Location shardLoc = center.clone();
                (new BukkitRunnable() {
                    int ticks = 0;
                    Location loc = shardLoc.clone();

                    public void run() {
                        if (this.ticks > 10) {
                            this.cancel();
                        } else {
                            this.loc.add(dir);
                            world.spawnParticle(Particle.SNOWFLAKE, this.loc, 6, 0.1, 0.1, 0.1, 0.01);
                            world.spawnParticle(Particle.BLOCK, this.loc, 2, 0.1, 0.1, 0.1, (double)0.0F, Material.ICE.createBlockData());
                            world.spawnParticle(Particle.BLOCK, this.loc, 2, 0.1, 0.1, 0.1, (double)0.0F, Material.PACKED_ICE.createBlockData());
                            world.spawnParticle(Particle.CLOUD, this.loc, 1, 0.05, 0.05, 0.05, 0.01);
                            world.playSound(this.loc, Sound.BLOCK_GLASS_BREAK, 0.5F, 1.7F);
                            ++this.ticks;
                        }
                    }
                }).runTaskTimer(Bukkit.getPluginManager().getPlugin("FlameSMPlugin"), 0L, 1L);
            }

            world.spawnParticle(Particle.SNOWFLAKE, center, 30, 0.7, 0.7, 0.7, 0.1);
            world.spawnParticle(Particle.SNOWFLAKE, center, 20, (double)0.5F, (double)0.5F, (double)0.5F, 0.05);
            world.playSound(center, Sound.BLOCK_GLASS_BREAK, 1.2F, 1.5F);
            world.playSound(center, Sound.BLOCK_SNOW_BREAK, 0.7F, 1.2F);
        }
    }
}
