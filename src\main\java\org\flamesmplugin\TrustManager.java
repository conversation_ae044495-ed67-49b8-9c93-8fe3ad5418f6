package org.flamesmplugin;

import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;

import java.io.File;
import java.io.IOException;
import java.util.*;

/**
 * Manages player trust relationships with persistent storage
 */
public class TrustManager {
    private final FlameSMPlugin plugin;
    private final Map<UUID, Set<UUID>> trustRelationships = new HashMap<>();
    private File trustFile;
    private FileConfiguration trustConfig;
    
    public TrustManager(FlameSMPlugin plugin) {
        this.plugin = plugin;
        setupTrustFile();
        loadTrustData();
    }
    
    private void setupTrustFile() {
        trustFile = new File(plugin.getDataFolder(), "trust.yml");
        if (!trustFile.exists()) {
            try {
                trustFile.createNewFile();
            } catch (IOException e) {
                plugin.getLogger().severe("Could not create trust.yml file!");
                e.printStackTrace();
            }
        }
        trustConfig = YamlConfiguration.loadConfiguration(trustFile);
    }
    
    /**
     * Add a trust relationship
     */
    public boolean addTrust(Player truster, Player trusted) {
        UUID trusterUUID = truster.getUniqueId();
        UUID trustedUUID = trusted.getUniqueId();
        
        // Can't trust yourself
        if (trusterUUID.equals(trustedUUID)) {
            return false;
        }
        
        trustRelationships.computeIfAbsent(trusterUUID, k -> new HashSet<>()).add(trustedUUID);
        saveTrustData();
        return true;
    }
    
    /**
     * Remove a trust relationship
     */
    public boolean removeTrust(Player truster, Player trusted) {
        UUID trusterUUID = truster.getUniqueId();
        UUID trustedUUID = trusted.getUniqueId();
        
        Set<UUID> trustedPlayers = trustRelationships.get(trusterUUID);
        if (trustedPlayers != null && trustedPlayers.remove(trustedUUID)) {
            if (trustedPlayers.isEmpty()) {
                trustRelationships.remove(trusterUUID);
            }
            saveTrustData();
            return true;
        }
        return false;
    }
    
    /**
     * Check if player1 trusts player2
     */
    public boolean isTrusted(Player truster, Player trusted) {
        if (truster == null || trusted == null) return false;
        
        UUID trusterUUID = truster.getUniqueId();
        UUID trustedUUID = trusted.getUniqueId();
        
        Set<UUID> trustedPlayers = trustRelationships.get(trusterUUID);
        return trustedPlayers != null && trustedPlayers.contains(trustedUUID);
    }
    
    /**
     * Check if two players have mutual trust
     */
    public boolean isMutualTrust(Player player1, Player player2) {
        return isTrusted(player1, player2) && isTrusted(player2, player1);
    }
    
    /**
     * Get all players that the given player trusts
     */
    public Set<UUID> getTrustedPlayers(Player player) {
        Set<UUID> trusted = trustRelationships.get(player.getUniqueId());
        return trusted != null ? new HashSet<>(trusted) : new HashSet<>();
    }
    
    /**
     * Get all players that trust the given player
     */
    public Set<UUID> getPlayersThatTrust(Player player) {
        Set<UUID> trusters = new HashSet<>();
        UUID playerUUID = player.getUniqueId();
        
        for (Map.Entry<UUID, Set<UUID>> entry : trustRelationships.entrySet()) {
            if (entry.getValue().contains(playerUUID)) {
                trusters.add(entry.getKey());
            }
        }
        return trusters;
    }
    
    /**
     * Clear all trust relationships for a player
     */
    public void clearAllTrust(Player player) {
        UUID playerUUID = player.getUniqueId();
        
        // Remove player from others' trust lists
        for (Set<UUID> trustedSet : trustRelationships.values()) {
            trustedSet.remove(playerUUID);
        }
        
        // Remove player's own trust list
        trustRelationships.remove(playerUUID);
        
        // Clean up empty sets
        trustRelationships.entrySet().removeIf(entry -> entry.getValue().isEmpty());
        
        saveTrustData();
    }
    
    /**
     * Save trust data to file
     */
    private void saveTrustData() {
        trustConfig.set("trust", null); // Clear existing data
        
        for (Map.Entry<UUID, Set<UUID>> entry : trustRelationships.entrySet()) {
            String trusterPath = "trust." + entry.getKey().toString();
            List<String> trustedList = new ArrayList<>();
            for (UUID trusted : entry.getValue()) {
                trustedList.add(trusted.toString());
            }
            trustConfig.set(trusterPath, trustedList);
        }
        
        try {
            trustConfig.save(trustFile);
        } catch (IOException e) {
            plugin.getLogger().severe("Could not save trust data!");
            e.printStackTrace();
        }
    }
    
    /**
     * Load trust data from file
     */
    private void loadTrustData() {
        if (trustConfig.getConfigurationSection("trust") == null) return;
        
        for (String trusterString : trustConfig.getConfigurationSection("trust").getKeys(false)) {
            try {
                UUID trusterUUID = UUID.fromString(trusterString);
                List<String> trustedList = trustConfig.getStringList("trust." + trusterString);
                
                Set<UUID> trustedSet = new HashSet<>();
                for (String trustedString : trustedList) {
                    try {
                        trustedSet.add(UUID.fromString(trustedString));
                    } catch (IllegalArgumentException e) {
                        plugin.getLogger().warning("Invalid UUID in trust data: " + trustedString);
                    }
                }
                
                if (!trustedSet.isEmpty()) {
                    trustRelationships.put(trusterUUID, trustedSet);
                }
            } catch (IllegalArgumentException e) {
                plugin.getLogger().warning("Invalid UUID in trust data: " + trusterString);
            }
        }
        
        plugin.getLogger().info("Loaded " + trustRelationships.size() + " trust relationships");
    }
}
