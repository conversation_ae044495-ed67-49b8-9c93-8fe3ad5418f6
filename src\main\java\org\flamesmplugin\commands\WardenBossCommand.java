//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.flamesmplugin.commands;

import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.flamesmplugin.WardenBoss;

public class WardenBossCommand implements CommandExecutor {
    public boolean onCommand(CommandSender sender, Command cmd, String label, String[] args) {
        if (!(sender instanceof Player player)) {
            sender.sendMessage("§cOnly players can use this command.");
            return true;
        }
        final Location spawnLocFinal;
        Location spawnLocTemp = player.getLocation();
        String[] coords = args;
        if (args.length == 1 && args[0].contains(",")) {
            coords = args[0].split(",");
        }
        if (coords.length == 3) {
            try {
                String sx = coords[0].trim().replace(",", "");
                String sy = coords[1].trim().replace(",", "");
                String sz = coords[2].trim().replace(",", "");
                double x = Double.parseDouble(sx);
                double y = Double.parseDouble(sy);
                double z = Double.parseDouble(sz);
                if (Double.isNaN(x) || Double.isNaN(y) || Double.isNaN(z) || Double.isInfinite(x) || Double.isInfinite(y) || Double.isInfinite(z)) {
                    player.sendMessage("§cInvalid coordinates (not finite): " + sx + ", " + sy + ", " + sz);
                    return true;
                }
                World world = player.getWorld();
                spawnLocTemp = new Location(world, x, y, z);
            } catch (NumberFormatException var18) {
                player.sendMessage("§cInvalid coordinates. Usage: /flame wardenboss [x y z]");
                return true;
            }
        } else if (args.length != 0) {
            player.sendMessage("§eUsage: /flame wardenboss [x y z]");
            return true;
        }
        spawnLocFinal = spawnLocTemp;

        // 1. Visual shockwave under every player (unchanged speed for FX)
        for (Player p : player.getServer().getOnlinePlayers()) {
            org.bukkit.Location base = p.getLocation().clone();
            base.setY(base.getY() - 1.0);
            org.flamesmplugin.ParticleUtils.createCircle(base, org.bukkit.Particle.SONIC_BOOM, 6.0, 48);
            org.flamesmplugin.ParticleUtils.createCircle(base, org.bukkit.Particle.CLOUD, 6.0, 32);
            org.flamesmplugin.ParticleUtils.createCircle(base, org.bukkit.Particle.EXPLOSION, 6.0, 24);
            p.getWorld().playSound(base, org.bukkit.Sound.ENTITY_WARDEN_ROAR, 1.2F, 0.7F);
        }

        // 2. Loop server time: night, noon, midnight (exponentially faster)
        World world = spawnLocFinal.getWorld();
        int[] times = {18000, 6000, 0};
        int cycles = 15; // More cycles for ~9s
        int tickDelay = 0;
        int baseDelay = 4; // 0.2s per phase
        for (int c = 0; c < cycles; c++) {
            for (int i = 0; i < times.length; i++) {
                int time = times[i];
                int thisDelay = baseDelay;
                org.bukkit.Bukkit.getScheduler().runTaskLater(org.bukkit.Bukkit.getPluginManager().getPlugin("FlameSMPlugin"), () -> {
                    world.setTime(time);
                }, tickDelay);
                tickDelay += thisDelay;
            }
        }

        // 3. After time loop, spawn boss with sound and message
        int totalDelay = tickDelay + 10; // +0.5s buffer
        org.bukkit.Bukkit.getScheduler().runTaskLater(org.bukkit.Bukkit.getPluginManager().getPlugin("FlameSMPlugin"), () -> {
            world.playSound(spawnLocFinal, org.bukkit.Sound.ENTITY_ENDER_DRAGON_GROWL, 10.0F, 0.5F);
            for (Player online : org.bukkit.Bukkit.getOnlinePlayers()) {
                online.sendMessage("§5§l⚠️ THE VOID WARDEN HAS AWAKENED! §dPrepare for darkness...");
            }
            org.flamesmplugin.WardenBoss.spawnWardenBoss(spawnLocFinal);
        }, totalDelay);

        player.sendMessage("§7Warden Boss ritual started at " + spawnLocFinal.getBlockX() + ", " + spawnLocFinal.getBlockY() + ", " + spawnLocFinal.getBlockZ());
        return true;
    }
}
