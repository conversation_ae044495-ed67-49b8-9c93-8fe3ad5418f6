//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.flamesmplugin;

import java.util.HashSet;
import java.util.Set;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.World;
import org.bukkit.attribute.Attribute;
import org.bukkit.boss.BarColor;
import org.bukkit.boss.BarFlag;
import org.bukkit.boss.BarStyle;
import org.bukkit.boss.BossBar;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Fireball;
import org.bukkit.entity.Player;
import org.bukkit.entity.Warden;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityDeathEvent;
import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.Vector;

public class WardenBoss implements Listener {
    private final JavaPlugin plugin;
    private Warden warden;
    private BossBar bossBar;
    private final Set<Player> playersInRange = new HashSet<>();
    private boolean isActive = false;

    // State machine for custom AI
    private enum State {
        STALKING, CHARGING, BESERK, RAGE;

        public String getDisplayName() {
            return switch (this) {
                case STALKING -> "§b§lSTALKING";
                case CHARGING -> "§e§lCHARGING";
                case BESERK -> "§c§lBESERK";
                case RAGE -> "§5§lRAGE";
            };
        }
    }
    private State currentState = State.STALKING;
    private long lastStateChange = 0L;
    private Player stalkingTarget = null;
    private Player chargingTarget = null;
    private int beserkRetargetTicks = 0;
    private int ragePulseTicks = 0;
    private boolean rageLightsOut = false;

    public WardenBoss(JavaPlugin plugin) {
        this.plugin = plugin;
    }

    public void spawn(Location loc) {
        if (!this.isActive) {
            World world = loc.getWorld();
            this.warden = (Warden)world.spawnEntity(loc, EntityType.WARDEN);
            // Disable vanilla AI so only our custom AI runs, but allow movement
            this.warden.setAI(true); // Enable AI for movement physics
            // Remove all vanilla goals to prevent default behavior
            try {
                Object nmsWarden = this.warden.getClass().getMethod("getHandle").invoke(this.warden);
                java.lang.reflect.Field goalSelectorField = nmsWarden.getClass().getField("goalSelector");
                Object goalSelector = goalSelectorField.get(nmsWarden);
                goalSelector.getClass().getMethod("a").invoke(goalSelector); // Clear goals
            } catch (Throwable t) {
                // If NMS fails, fallback to just enabling AI for movement
            }
            this.warden.getAttribute(Attribute.GENERIC_MAX_HEALTH).setBaseValue(2048.0);
            this.warden.setHealth(2048.0);
            // Face toward nearest player if possible
            Player nearest = null;
            double minDist = Double.MAX_VALUE;
            for (Player p : Bukkit.getOnlinePlayers()) {
                if (p.getWorld() == world) {
                    double d = p.getLocation().distance(loc);
                    if (d < minDist) { minDist = d; nearest = p; }
                }
            }
            if (nearest != null) {
                Location wardenLoc = this.warden.getLocation();
                wardenLoc.setDirection(nearest.getLocation().toVector().subtract(wardenLoc.toVector()));
                this.warden.teleport(wardenLoc);
            }
            try {
                net.kyori.adventure.text.Component name = net.kyori.adventure.text.Component.text("§9§lWarden Boss");
                this.warden.customName(name);
                this.warden.setCustomNameVisible(true);
            } catch (Throwable t) {
                this.warden.setCustomName("§9§lWarden Boss");
                this.warden.setCustomNameVisible(true);
            }
            this.warden.addPotionEffect(new PotionEffect(PotionEffectType.RESISTANCE, Integer.MAX_VALUE, 2, true, false));
            this.bossBar = Bukkit.createBossBar("§9§lWarden Boss", BarColor.BLUE, BarStyle.SEGMENTED_10, new BarFlag[0]);
            this.bossBar.setProgress(1.0);
            this.bossBar.setVisible(true);
            this.isActive = true;
            this.currentState = State.STALKING;
            this.lastStateChange = System.currentTimeMillis();
            (new BossBarTask()).runTaskTimer(this.plugin, 0L, 10L);
            (new AbilityTask()).runTaskTimer(this.plugin, 0L, 240L);
            (new CustomAITask()).runTaskTimer(this.plugin, 0L, 5L);
        }
    }

    // Custom AI state machine
    private class CustomAITask extends BukkitRunnable {
        // Utility: get a random player in a world
        private Player getRandomPlayer(World world) {
            Player[] arr = Bukkit.getOnlinePlayers().stream()
                .filter(p -> p.getWorld() == world)
                .toArray(Player[]::new);
            if (arr.length == 0) return null;
            return arr[(int)(Math.random()*arr.length)];
        }

        // Utility: break blocks below the warden (leaves, fences, doors, torches, glass)
        private void breakBlocksBelow(Location loc) {
            for (int y = -1; y <= 1; y++) {
                Location below = loc.clone().add(0, y, 0);
                for (int dx = -1; dx <= 1; dx++) {
                    for (int dz = -1; dz <= 1; dz++) {
                        Location l = below.clone().add(dx, 0, dz);
                        org.bukkit.block.Block b = l.getBlock();
                        if (b.getType().toString().contains("LEAVES") || b.getType().toString().contains("FENCE") || b.getType().toString().contains("DOOR") || b.getType().toString().contains("TORCH") || b.getType().toString().contains("GLASS")) {
                            b.setType(org.bukkit.Material.AIR);
                        }
                    }
                }
            }
        }

        // Utility: remove nearby torches in a radius
        private void removeNearbyTorches(Location loc, int radius) {
            World w = loc.getWorld();
            for (int dx = -radius; dx <= radius; dx++) {
                for (int dz = -radius; dz <= radius; dz++) {
                    for (int dy = -2; dy <= 4; dy++) {
                        Location l = loc.clone().add(dx, dy, dz);
                        org.bukkit.block.Block b = w.getBlockAt(l);
                        if (b.getType().toString().contains("TORCH")) {
                            b.setType(org.bukkit.Material.AIR);
                        }
                    }
                }
            }
        }
        // Move the warden toward a target location at a given speed
        private void moveToward(Location target, double speed) {
            if (warden == null || warden.isDead()) return;
            Vector dir = target.toVector().subtract(warden.getLocation().toVector());
            if (dir.lengthSquared() == 0) return;
            dir.normalize();
            warden.setVelocity(dir.multiply(speed));
        }
        private int tick = 0;
        private long lastHitTime = System.currentTimeMillis();
        public void run() {
            if (warden == null || warden.isDead()) { this.cancel(); return; }
            double hp = warden.getHealth();
            double maxHp = warden.getAttribute(Attribute.GENERIC_MAX_HEALTH).getValue();
            // State transitions
            // If not currently charging, pick a target for charging
            if (currentState == State.STALKING && (System.currentTimeMillis() - lastHitTime > 15000)) {
                // Pick a target to charge
                chargingTarget = getNearestPlayer(warden.getLocation());
                if (chargingTarget != null) {
                    currentState = State.CHARGING;
                    lastStateChange = System.currentTimeMillis();
                }
            } else if (currentState != State.BESERK && currentState != State.RAGE && hp < maxHp * 0.5) {
                currentState = State.BESERK;
                lastStateChange = System.currentTimeMillis();
                for (Player p : Bukkit.getOnlinePlayers()) p.sendMessage("§c§lThe Warden goes BESERK!");
            } else if (currentState == State.BESERK && hp < maxHp * 0.25 && currentState != State.RAGE) {
                currentState = State.RAGE;
                lastStateChange = System.currentTimeMillis();
                ragePulseTicks = 0;
                rageLightsOut = false;
                for (Player p : Bukkit.getOnlinePlayers()) p.sendMessage("§5§lThe Warden enters RAGE!");
                spawnRageEndermen();
            }
            // State logic
            switch (currentState) {
                case STALKING -> handleStalking();
                case CHARGING -> handleCharging();
                case BESERK -> handleBeserk();
                case RAGE -> handleRage();
            }
        }
        // --- State Handlers ---
        private void handleStalking() {
            // Pick nearest player if not set
            if (stalkingTarget == null || !stalkingTarget.isOnline() || stalkingTarget.getWorld() != warden.getWorld()) {
                stalkingTarget = getNearestPlayer(warden.getLocation());
            }
            if (stalkingTarget != null) {
                // Follow slowly, no sprint
                moveToward(stalkingTarget.getLocation(), 0.18);
                // If player sprints, attacks, or breaks/places block nearby, switch to CHARGING
                // No auto-trigger to charging here; handled by timer in run()
            }
        }
        private void handleCharging() {
            if (chargingTarget == null || !chargingTarget.isOnline() || chargingTarget.getWorld() != warden.getWorld()) {
                currentState = State.STALKING;
                stalkingTarget = null;
                return;
            }
            // Freeze for 1s, then charge
            if (System.currentTimeMillis() - lastStateChange < 1000) {
                // Only visual telegraph, NO sound
                ParticleUtils.createCircle(warden.getLocation(), Particle.SONIC_BOOM, 5.5, 32);
                return;
            }
            // Charge at target
            Vector dir = chargingTarget.getLocation().toVector().subtract(warden.getLocation().toVector()).normalize();
            warden.setVelocity(dir.multiply(1.7).setY(0.3));
            warden.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, 40, 2, true, false));
            // Knockback and damage on hit
            if (warden.getLocation().distance(chargingTarget.getLocation()) < 2.5) {
                chargingTarget.damage(32.0, warden);
                chargingTarget.setVelocity(dir.multiply(2.2).setY(1.1));
                chargingTarget.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 60, 2));
                currentState = State.STALKING;
                stalkingTarget = null;
                chargingTarget = null;
                lastHitTime = System.currentTimeMillis();
            }
        }
        private void handleBeserk() {
            // Moves very fast, switches targets every 5s, wither/slowness on hit, ignores projectiles
            beserkRetargetTicks++;
            if (beserkRetargetTicks >= 20 * 5 || stalkingTarget == null || !stalkingTarget.isOnline()) {
                stalkingTarget = getRandomPlayer(warden.getWorld());
                beserkRetargetTicks = 0;
            }
            if (stalkingTarget != null) {
                moveToward(stalkingTarget.getLocation(), 0.38);
                // If close, apply wither/slowness (nausea removed)
                if (warden.getLocation().distance(stalkingTarget.getLocation()) < 2.5) {
                    stalkingTarget.addPotionEffect(new PotionEffect(PotionEffectType.WITHER, 60, 1));
                    stalkingTarget.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 60, 2));
                    stalkingTarget.damage(18.0, warden);
                    stalkingTarget.setVelocity(warden.getLocation().toVector().subtract(stalkingTarget.getLocation().toVector()).normalize().multiply(-1.2).setY(0.7));
                    // No sound
                }
                lastHitTime = System.currentTimeMillis();
            }
            // Break blocks in way (leaves, fences, doors, torches)
            breakBlocksBelow(warden.getLocation());
        }
        // --- RAGE phase handler ---
        private void handleRage() {
            ragePulseTicks++;
            if (!rageLightsOut) {
                // Remove torches in 12 block radius
                removeNearbyTorches(warden.getLocation(), 12);
                rageLightsOut = true;
            }
            // Shadow pulse every 8s
            if (ragePulseTicks % (8 * 20 / 5) == 0) {
                ParticleUtils.createExplosion(warden.getLocation(), Particle.SCULK_SOUL, 40);
                for (Player p : Bukkit.getOnlinePlayers()) {
                    if (p.getWorld() == warden.getWorld() && p.getLocation().distance(warden.getLocation()) < 18.0) {
                        p.addPotionEffect(new PotionEffect(PotionEffectType.BLINDNESS, 60, 1));
                        p.addPotionEffect(new PotionEffect(PotionEffectType.WITHER, 40, 1));
                        p.sendMessage("§8§lThe RAGE pulses...");
                        if (Math.random() < 0.33) {
                            freezePlayer(p, 40);
                        }
                    }
                }
                // Teleport randomly within 10 blocks
                Location tp = warden.getLocation().clone().add((Math.random()-0.5)*20, 0, (Math.random()-0.5)*20);
                tp.setY(warden.getWorld().getHighestBlockYAt(tp));
                warden.teleport(tp);
                // No sound
                // Regen a bit
                warden.setHealth(Math.min(warden.getHealth() + 16.0, warden.getAttribute(Attribute.GENERIC_MAX_HEALTH).getValue()));
            }
            // New RAGE ability: spawn 30 endermen targeting nearest players every 16s
            if (ragePulseTicks % (16 * 20 / 5) == 0) {
                spawnRageEndermen();
                for (Player p : Bukkit.getOnlinePlayers()) {
                    if (p.getWorld() == warden.getWorld() && p.getLocation().distance(warden.getLocation()) < 30.0) {
                        p.sendMessage("§5§lThe Warden's RAGE summons Endermen!");
                    }
                }
            }
        }

        // Spawns 30 endermen targeting nearest players during RAGE
        private void spawnRageEndermen() {
            Location center = warden.getLocation();
            World world = warden.getWorld();
            for (int i = 0; i < 30; i++) {
                Location spawnLoc = center.clone().add((Math.random()-0.5)*16, 0, (Math.random()-0.5)*16);
                spawnLoc.setY(world.getHighestBlockYAt(spawnLoc) + 1);
                org.bukkit.entity.Enderman enderman = (org.bukkit.entity.Enderman)world.spawnEntity(spawnLoc, EntityType.ENDERMAN);
                Player nearest = getNearestPlayer(spawnLoc);
                if (nearest != null) {
                    enderman.setTarget(nearest);
                }
            }
        }

        private void freezePlayer(Player p, int ticks) {
            float oldSpeed = p.getWalkSpeed();
            p.setWalkSpeed(0f);
            Bukkit.getScheduler().runTaskLater(plugin, () -> {
                p.setWalkSpeed(oldSpeed);
            }, ticks);
        }
    }

    // Utility: get nearest player to a location (including creative)
    private Player getNearestPlayer(Location loc) {
        Player nearest = null;
        double minDist = Double.MAX_VALUE;
        for (Player p : Bukkit.getOnlinePlayers()) {
            if (p.getWorld() == loc.getWorld()) {
                double d = p.getLocation().distance(loc);
                if (d < minDist) { minDist = d; nearest = p; }
            }
        }
        return nearest;
    }

    public static void spawnWardenBoss(Location loc) {
        JavaPlugin plugin = JavaPlugin.getProvidingPlugin(WardenBoss.class);
        WardenBoss boss = new WardenBoss(plugin);
        plugin.getServer().getPluginManager().registerEvents(boss, plugin);
        boss.spawn(loc);
    }

    @EventHandler
    public void onWardenDeath(EntityDeathEvent event) {
        if (this.warden != null && event.getEntity().equals(this.warden)) {
            World world = this.warden.getWorld();
            Location loc = this.warden.getLocation();
            FlameItems items = new FlameItems((FlameSMPlugin)this.plugin);

            // Announce killer
            if (event.getEntity().getKiller() != null) {
                Player killer = event.getEntity().getKiller();
                for (Player p : Bukkit.getOnlinePlayers()) {
                    p.sendMessage("§6The Warden Boss was slain by §e" + killer.getName() + "§6!");
                }
            } else {
                for (Player p : Bukkit.getOnlinePlayers()) {
                    p.sendMessage("§6The Warden Boss has been defeated!");
                }
            }

            // Drop 20-25 upgraders randomly around the death location
            int upgraders = 20 + (int)(Math.random() * 6); // 20 to 25
            for (int i = 0; i < upgraders; ++i) {
                Location dropLoc = loc.clone().add((Math.random()-0.5)*6, 1.5+Math.random()*2, (Math.random()-0.5)*6);
                world.dropItemNaturally(dropLoc, items.createFlameUpgrader());
            }

            // Drop exactly one mace
            Location maceLoc = loc.clone().add((Math.random()-0.5)*3, 1.5+Math.random()*2, (Math.random()-0.5)*3);
            world.dropItemNaturally(maceLoc, items.createFlameMace());

            this.bossBar.setVisible(false);
            this.isActive = false;
        }

    }

    @EventHandler
    public void onWardenDamage(EntityDamageByEntityEvent event) {
        if (this.warden != null && event.getEntity().equals(this.warden)) {
            if (event.getDamager() instanceof Player) {
                Player p = (Player)event.getDamager();
                p.sendMessage("§c§lno way you hit it");
            }
            // Reset AI hit timer
            if (this.currentState == State.STALKING || this.currentState == State.CHARGING || this.currentState == State.BESERK) {
                // Find the CustomAITask instance and update lastHitTime
                // (We use a static reference for simplicity)
                // But here, just update the field directly
                try {
                    java.lang.reflect.Field f = CustomAITask.class.getDeclaredField("lastHitTime");
                    f.setAccessible(true);
                    f.set(this, System.currentTimeMillis());
                } catch (Exception ignored) {}
            }
        }
    }

    private class BossBarTask extends BukkitRunnable {
        public void run() {
            if (WardenBoss.this.warden != null && !WardenBoss.this.warden.isDead()) {
                double progress = WardenBoss.this.warden.getHealth() / 2048.0;
                progress = Math.max(0.0, Math.min(1.0, progress));
                WardenBoss.this.bossBar.setProgress(progress);

                for(Player p : Bukkit.getOnlinePlayers()) {
                    if (p.getWorld().equals(WardenBoss.this.warden.getWorld()) && p.getLocation().distance(WardenBoss.this.warden.getLocation()) < 40.0) {
                        WardenBoss.this.bossBar.addPlayer(p);
                        WardenBoss.this.playersInRange.add(p);
                        // Show action bar with current phase
                        showPhaseActionBar(p);
                    } else {
                        WardenBoss.this.bossBar.removePlayer(p);
                        WardenBoss.this.playersInRange.remove(p);
                    }
                }

            } else {
                WardenBoss.this.bossBar.setVisible(false);
                this.cancel();
            }
        }
    }

    // Show the Warden's current phase in the action bar for a player
    private void showPhaseActionBar(Player p) {
        String msg = "§8[§dWarden Phase: §r" + currentState.getDisplayName() + "§8]";
        try {
            // Adventure API (modern Spigot/Paper)
            net.kyori.adventure.text.Component comp = net.kyori.adventure.text.Component.text(msg);
            p.sendActionBar(comp);
        } catch (Throwable t) {
            // Fallback for legacy Spigot
            p.sendActionBar(msg);
        }
    }

    private class AbilityTask extends BukkitRunnable {
        private int abilityIndex = 0;

        public void run() {
            if (WardenBoss.this.warden != null && !WardenBoss.this.warden.isDead()) {
                Location center = WardenBoss.this.warden.getLocation().clone();
                int warnTicks = 35;
                for(Player p : WardenBoss.this.playersInRange) {
                    if (p.isOnline() && p.getWorld().equals(center.getWorld())) {
                        switch (this.abilityIndex) {
                            case 0 -> { // Sonic Boom: beam
                                float yaw = center.getYaw();
                                ParticleUtils.showPreAttackBeam(center, yaw, 30.0, 2.0, warnTicks, p);
                            }
                            case 1 -> { // Ground Slam: circle
                                ParticleUtils.showPreAttackCircle(center, 5.0, warnTicks, p);
                            }
                            case 2 -> { // Shockwave: ring
                                ParticleUtils.showPreAttackRing(center, 7.5, 1.5, warnTicks, p);
                            }
                            case 3 -> { // Darkness Pulse: large circle
                                ParticleUtils.showPreAttackCircle(center, 8.0, warnTicks, p);
                            }
                            case 4 -> { // Meteor Rain: random circles
                                for (int i = 0; i < 6; ++i) {
                                    double angle = Math.random() * 2.0 * Math.PI;
                                    double dist = 3.0 + Math.random() * 8.0;
                                    Location target = center.clone().add(Math.cos(angle) * dist, 0.0, Math.sin(angle) * dist);
                                    ParticleUtils.showPreAttackCircle(target, 1.5, warnTicks, p);
                                }
                            }
                        }
                    }
                }
                (new BukkitRunnable() {
                    public void run() {
                        switch (AbilityTask.this.abilityIndex) {
                            case 0 -> AbilityTask.this.sonicBoom();
                            case 1 -> AbilityTask.this.groundSlam();
                            case 2 -> AbilityTask.this.shockwave();
                            case 3 -> AbilityTask.this.darknessPulse();
                            case 4 -> AbilityTask.this.meteorRain();
                        }
                        AbilityTask.this.abilityIndex = (AbilityTask.this.abilityIndex + 1) % 5;
                    }
                }).runTaskLater(WardenBoss.this.plugin, (long)warnTicks);
            } else {
                this.cancel();
            }
        }

        private void meteorRain() {
            // No sound

            for(int i = 0; i < 6; ++i) {
                double angle = Math.random() * (double)2.0F * Math.PI;
                double dist = (double)3.0F + Math.random() * (double)8.0F;
                final Location target = WardenBoss.this.warden.getLocation().clone().add(Math.cos(angle) * dist, (double)0.0F, Math.sin(angle) * dist);

                for(Player p : WardenBoss.this.playersInRange) {
                    if (p.isOnline() && p.getWorld().equals(target.getWorld())) {
                        ParticleUtils.createCircle(target, Particle.LAVA, (double)1.5F, 24);
                        ParticleUtils.createCircle(target, Particle.FLAME, (double)1.5F, 24);
                    }
                }

                (new BukkitRunnable() {
                    public void run() {
                        Location spawnLoc = target.clone().add((double)0.0F, (double)18.0F, (double)0.0F);
                        final Fireball fireball = (Fireball)spawnLoc.getWorld().spawn(spawnLoc, Fireball.class);
                        fireball.setDirection(target.clone().subtract(spawnLoc).toVector().normalize());
                        fireball.setYield(2.0F);
                        fireball.setIsIncendiary(true);
                        fireball.setVelocity(target.clone().subtract(spawnLoc).toVector().normalize().multiply(1.2));
                        try {
                            net.kyori.adventure.text.Component fbName = net.kyori.adventure.text.Component.text("warden_meteor");
                            fireball.customName(fbName);
                            fireball.setCustomNameVisible(false);
                        } catch (Throwable t) {
                            // Fallback for older API (deprecated)
                            fireball.setCustomName("warden_meteor");
                            fireball.setCustomNameVisible(false);
                        }
                        (new BukkitRunnable() {
                            public void run() {
                                if (!fireball.isDead() && !fireball.isOnGround()) {
                                    Location loc = fireball.getLocation();
                                    loc.getWorld().spawnParticle(Particle.FLAME, loc, 8, 0.2, 0.2, 0.2, 0.01);
                                    loc.getWorld().spawnParticle(Particle.LAVA, loc, 2, 0.1, 0.1, 0.1, 0.01);
                                    loc.getWorld().spawnParticle(Particle.CAMPFIRE_COSY_SMOKE, loc, 2, 0.2, 0.2, 0.2, 0.01);
                                } else {
                                    this.cancel();
                                }
                            }
                        }).runTaskTimer(WardenBoss.this.plugin, 0L, 1L);
                    }
                }).runTaskLater(WardenBoss.this.plugin, 18L);
            }

        }

        private void shockwave() {
            // No sound
            // Expanding shockwave ring
            ParticleUtils.createCircle(WardenBoss.this.warden.getLocation(), Particle.SONIC_BOOM, 7.5, 48);
            ParticleUtils.createCircle(WardenBoss.this.warden.getLocation(), Particle.EXPLOSION, 7.5, 32);
            ParticleUtils.createCircle(WardenBoss.this.warden.getLocation(), Particle.CLOUD, 7.5, 32);
            for (double r = 2.0; r <= 7.5; r += 1.5) {
                ParticleUtils.createCircle(WardenBoss.this.warden.getLocation(), Particle.CLOUD, r, 24);
            }
            for(Player p : WardenBoss.this.playersInRange) {
                if (p.isOnline() && p.getLocation().distance(WardenBoss.this.warden.getLocation()) < 18.0) {
                    p.damage(44.0, WardenBoss.this.warden); // Increased from 36.0 to 44.0
                    p.setVelocity(p.getLocation().toVector().subtract(WardenBoss.this.warden.getLocation().toVector()).normalize().multiply(1.2).setY(0.7));
                    p.sendMessage("§ci have no clue what to make this msg say but uhh yeah");
                }
            }
        }

        private void sonicBoom() {
            // Sonic Boom: long beam in facing direction
            Location start = WardenBoss.this.warden.getLocation().clone().add(0, 1.5, 0);
            float yaw = WardenBoss.this.warden.getLocation().getYaw();
            Vector dir = new Vector(-Math.sin(Math.toRadians(yaw)), 0, Math.cos(Math.toRadians(yaw)));
            double length = 30.0;
            for (double d = 0; d < length; d += 0.5) {
                Location point = start.clone().add(dir.clone().multiply(d));
                WardenBoss.this.warden.getWorld().spawnParticle(Particle.SONIC_BOOM, point, 1, 0.1, 0.1, 0.1, 0.01);
                if (d % 2 == 0) {
                    WardenBoss.this.warden.getWorld().spawnParticle(Particle.END_ROD, point, 2, 0.2, 0.2, 0.2, 0.01);
                }
            }
            for(Player p : WardenBoss.this.playersInRange) {
                if (p.isOnline() && p.getLocation().distance(WardenBoss.this.warden.getLocation()) < 30.0) {
                    p.damage(19.0, WardenBoss.this.warden);
                    p.setVelocity(p.getLocation().toVector().subtract(WardenBoss.this.warden.getLocation().toVector()).normalize().multiply(2).setY(1));
                    p.sendMessage("§bkaboom you just got sent to space");
                }
            }
            // No sound
        }

        private void groundSlam() {
            // No sound
            // Upward burst and shockwave ring
            ParticleUtils.createExplosion(WardenBoss.this.warden.getLocation().add(0, 0.5, 0), Particle.EXPLOSION, 40);
            ParticleUtils.createCircle(WardenBoss.this.warden.getLocation(), Particle.CLOUD, 5.0, 32);
            ParticleUtils.createCircle(WardenBoss.this.warden.getLocation(), Particle.BLOCK, 5.0, 32, org.bukkit.Material.DEEPSLATE);
            for (double r = 2.0; r <= 5.0; r += 1.0) {
                ParticleUtils.createCircle(WardenBoss.this.warden.getLocation(), Particle.CLOUD, r, 24);
            }
            for(Player p : WardenBoss.this.playersInRange) {
                if (p.isOnline() && p.getLocation().distance(WardenBoss.this.warden.getLocation()) < 10.0) {
                    p.damage(20.0, WardenBoss.this.warden);
                    p.setVelocity(new Vector(0.0, 2.5, 0.0));
                    p.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 60, 4));
                    p.sendMessage("§cSo the warden is kinda fat, so it just took a step and yeah...");
                }
            }
        }

        private void darknessPulse() {
            // No sound
            // Expanding sphere of darkness
            Location center = WardenBoss.this.warden.getLocation();
            for (double r = 2.0; r <= 8.0; r += 1.5) {
                ParticleUtils.createCircle(center, Particle.SCULK_SOUL, r, 32);
                ParticleUtils.createCircle(center, Particle.SMOKE, r, 24);
            }
            ParticleUtils.createExplosion(center, Particle.SCULK_SOUL, 30);
            for(Player p : WardenBoss.this.playersInRange) {
                if (p.isOnline() && p.getLocation().distance(center) < 20.0) {
                    p.addPotionEffect(new PotionEffect(PotionEffectType.BLINDNESS, 80, 1));
                    p.addPotionEffect(new PotionEffect(PotionEffectType.WITHER, 60, 1));
                    p.sendMessage("§8For some reason i added this blindness thing so uhh...");
                }
            }
        }
    }
}
