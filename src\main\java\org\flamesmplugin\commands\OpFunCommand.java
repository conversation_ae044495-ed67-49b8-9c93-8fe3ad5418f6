//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

package org.flamesmplugin.commands;

import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.flamesmplugin.CooldownManager;

public class OpFunCommand implements CommandExecutor {
    private final CooldownManager cooldownManager;

    public OpFunCommand(CooldownManager cooldownManager) {
        this.cooldownManager = cooldownManager;
    }

    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player player)) {
            sender.sendMessage("Only players can use this command.");
            return true;
        } else if (!player.hasPermission("flame.admin")) {
            player.sendMessage("§cYou don't have permission to use this command!");
            return true;
        } else {
            this.cooldownManager.removeCooldown(player);
            player.sendMessage("§aAll flame ability cooldowns have been removed for you!");
            return true;
        }
    }
}
