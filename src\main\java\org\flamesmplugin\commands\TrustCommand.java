package org.flamesmplugin.commands;

import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;
import org.flamesmplugin.TrustManager;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Command handler for the /trust system
 */
public class TrustCommand implements CommandExecutor, TabCompleter {
    private final TrustManager trustManager;
    
    public TrustCommand(TrustManager trustManager) {
        this.trustManager = trustManager;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§cOnly players can use trust commands!");
            return true;
        }
        
        Player player = (Player) sender;
        
        if (args.length == 0) {
            sendHelp(player);
            return true;
        }
        
        switch (args[0].toLowerCase()) {
            case "add":
                return handleAdd(player, args);
            case "remove":
            case "rem":
                return handleRemove(player, args);
            case "list":
                return handleList(player);
            case "clear":
                return handleClear(player);
            case "mutual":
                return handleMutual(player, args);
            default:
                sendHelp(player);
                return true;
        }
    }
    
    private boolean handleAdd(Player player, String[] args) {
        if (args.length < 2) {
            player.sendMessage("§cUsage: /trust add <player>");
            return true;
        }
        
        Player target = Bukkit.getPlayer(args[1]);
        if (target == null) {
            player.sendMessage("§cPlayer not found: " + args[1]);
            return true;
        }
        
        if (player.equals(target)) {
            player.sendMessage("§cYou cannot trust yourself!");
            return true;
        }
        
        if (trustManager.isTrusted(player, target)) {
            player.sendMessage("§eYou already trust " + target.getName());
            return true;
        }
        
        if (trustManager.addTrust(player, target)) {
            player.sendMessage("§aYou now trust " + target.getName());
            target.sendMessage("§6" + player.getName() + " now trusts you!");
            
            // Check for mutual trust
            if (trustManager.isTrusted(target, player)) {
                player.sendMessage("§d§lMutual trust established with " + target.getName() + "!");
                target.sendMessage("§d§lMutual trust established with " + player.getName() + "!");
            }
        } else {
            player.sendMessage("§cFailed to add trust relationship");
        }
        
        return true;
    }
    
    private boolean handleRemove(Player player, String[] args) {
        if (args.length < 2) {
            player.sendMessage("§cUsage: /trust remove <player>");
            return true;
        }
        
        Player target = Bukkit.getPlayer(args[1]);
        if (target == null) {
            player.sendMessage("§cPlayer not found: " + args[1]);
            return true;
        }
        
        if (trustManager.removeTrust(player, target)) {
            player.sendMessage("§cYou no longer trust " + target.getName());
            target.sendMessage("§c" + player.getName() + " no longer trusts you");
        } else {
            player.sendMessage("§eYou don't trust " + target.getName());
        }
        
        return true;
    }
    
    private boolean handleList(Player player) {
        Set<UUID> trustedPlayers = trustManager.getTrustedPlayers(player);
        Set<UUID> trusters = trustManager.getPlayersThatTrust(player);
        
        player.sendMessage("§6=== Trust Relationships ===");
        
        if (trustedPlayers.isEmpty()) {
            player.sendMessage("§7You don't trust anyone");
        } else {
            player.sendMessage("§aYou trust:");
            for (UUID uuid : trustedPlayers) {
                Player trusted = Bukkit.getPlayer(uuid);
                String name = trusted != null ? trusted.getName() : "§7(Offline)";
                boolean mutual = trusters.contains(uuid);
                String mutualIndicator = mutual ? " §d[Mutual]" : "";
                player.sendMessage("§7- " + name + mutualIndicator);
            }
        }
        
        if (trusters.isEmpty()) {
            player.sendMessage("§7No one trusts you");
        } else {
            player.sendMessage("§bPlayers that trust you:");
            for (UUID uuid : trusters) {
                Player truster = Bukkit.getPlayer(uuid);
                String name = truster != null ? truster.getName() : "§7(Offline)";
                boolean mutual = trustedPlayers.contains(uuid);
                String mutualIndicator = mutual ? " §d[Mutual]" : "";
                player.sendMessage("§7- " + name + mutualIndicator);
            }
        }
        
        return true;
    }
    
    private boolean handleClear(Player player) {
        trustManager.clearAllTrust(player);
        player.sendMessage("§cAll trust relationships cleared!");
        return true;
    }
    
    private boolean handleMutual(Player player, String[] args) {
        if (args.length < 2) {
            player.sendMessage("§cUsage: /trust mutual <player>");
            return true;
        }
        
        Player target = Bukkit.getPlayer(args[1]);
        if (target == null) {
            player.sendMessage("§cPlayer not found: " + args[1]);
            return true;
        }
        
        if (player.equals(target)) {
            player.sendMessage("§cYou cannot check mutual trust with yourself!");
            return true;
        }
        
        boolean youTrustThem = trustManager.isTrusted(player, target);
        boolean theyTrustYou = trustManager.isTrusted(target, player);
        
        player.sendMessage("§6=== Trust Status with " + target.getName() + " ===");
        player.sendMessage("§7You trust them: " + (youTrustThem ? "§aYes" : "§cNo"));
        player.sendMessage("§7They trust you: " + (theyTrustYou ? "§aYes" : "§cNo"));
        
        if (youTrustThem && theyTrustYou) {
            player.sendMessage("§d§lMutual trust established!");
        } else {
            player.sendMessage("§eNo mutual trust");
        }
        
        return true;
    }
    
    private void sendHelp(Player player) {
        player.sendMessage("§6=== Trust Commands ===");
        player.sendMessage("§7/trust add <player> - Trust a player");
        player.sendMessage("§7/trust remove <player> - Remove trust");
        player.sendMessage("§7/trust list - Show trust relationships");
        player.sendMessage("§7/trust mutual <player> - Check mutual trust");
        player.sendMessage("§7/trust clear - Clear all trust relationships");
        player.sendMessage("§e§lNote: §7Trusted players won't damage each other in PvP");
    }
    
    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        if (args.length == 1) {
            return Arrays.asList("add", "remove", "list", "clear", "mutual")
                    .stream()
                    .filter(s -> s.toLowerCase().startsWith(args[0].toLowerCase()))
                    .collect(Collectors.toList());
        }
        
        if (args.length == 2 && (args[0].equalsIgnoreCase("add") || 
                                args[0].equalsIgnoreCase("remove") || 
                                args[0].equalsIgnoreCase("mutual"))) {
            return Bukkit.getOnlinePlayers().stream()
                    .map(Player::getName)
                    .filter(name -> name.toLowerCase().startsWith(args[1].toLowerCase()))
                    .collect(Collectors.toList());
        }
        
        return new ArrayList<>();
    }
}
