name: FlameSMPlugin
version: '1.0-SNAPSHOT'
main: org.flamesmplugin.FlameSMPlugin
api-version: '1.21'
author: YourName
description: A flame-based ability system for Minecraft

commands:
  flame:
    description: Main command for flame system
    usage: /flame <subcommand>
    permission: flame.use
    aliases: [flames, f]
    subcommands:
      give:
        description: Give a custom flame item (match, mace, upgrader, shield)
        usage: /flame give <player> <item>

  opfun:
    description: Remove cooldowns for yourself (admin only)
    usage: /opfun
    permission: flame.admin

  fun:
    description: Restore cooldowns for yourself (admin only)
    usage: /fun
    permission: flame.admin

  getrecipe:
    description: Gives all custom items for recipe viewing (admin only)
    usage: /getrecipe
    permission: flame.admin

  setabilitydamage:
    description: Set all flame ability damage values
    usage: /setabilitydamage <amount>
    permission: flamesm.setabilitydamage

  wardenboss:
    description: Spawn the Warden Boss at your location or at [x y z]
    usage: /flame wardenboss [x y z]
    permission: flame.admin

  resetflamelevel:
    description: Reset your flame level to 0
    usage: /resetflamelevel
    permission: flame.admin

  trust:
    description: Manage player trust relationships
    usage: /trust <add|remove|list|clear|mutual> [player]
    aliases: [t]

  nooutput:
    description: Test command for silent command execution
    usage: /nooutput
    permission: flame.admin

permissions:
  flame.use:
    description: Basic flame command usage
    default: true
  flame.admin:
    description: Administrative flame commands
    default: op
  flamesm.setabilitydamage:
    description: Allows setting ability damage
    default: op
