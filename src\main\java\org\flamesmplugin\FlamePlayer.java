//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.flamesmplugin;

import java.util.UUID;

public class FlamePlayer {
    private final UUID playerId;
    private FlameType flameType;
    private int upgradeLevel;
    private int deaths;
    private boolean flameExtinguished;
    private long lastAbilityUse;
    private int flameLevel;

    public FlamePlayer(UUID playerId) {
        this.playerId = playerId;
        this.flameType = null;
        this.upgradeLevel = 1;
        this.deaths = 0;
        this.flameExtinguished = false;
        this.lastAbilityUse = 0L;
        this.flameLevel = 0;
    }

    public FlamePlayer(UUID playerId, FlameType flameType, int upgradeLevel, int deaths, boolean flameExtinguished) {
        this.playerId = playerId;
        this.flameType = flameType;
        this.upgradeLevel = upgradeLevel;
        this.deaths = deaths;
        this.flameExtinguished = flameExtinguished;
        this.lastAbilityUse = 0L;
        this.flameLevel = 0;
    }

    public UUID getPlayerId() {
        return this.playerId;
    }

    public FlameType getFlameType() {
        return this.flameType;
    }

    public void setFlameType(FlameType flameType) {
        this.flameType = flameType;
    }

    public int getUpgradeLevel() {
        return this.upgradeLevel;
    }

    public void setUpgradeLevel(int upgradeLevel) {
        this.upgradeLevel = Math.max(1, Math.min(2, upgradeLevel));
    }

    public int getDeaths() {
        return this.deaths;
    }

    public void addDeath() {
        ++this.deaths;
        if (this.deaths >= 3) {
            this.flameExtinguished = true;
        }

    }

    public void resetDeaths() {
        this.deaths = 0;
    }

    public boolean isFlameExtinguished() {
        return this.flameExtinguished;
    }

    public void setFlameExtinguished(boolean flameExtinguished) {
        this.flameExtinguished = flameExtinguished;
    }

    public void relightFlame() {
        this.flameExtinguished = false;
        this.deaths = 0;
    }

    public long getLastAbilityUse() {
        return this.lastAbilityUse;
    }

    public void setLastAbilityUse(long lastAbilityUse) {
        this.lastAbilityUse = lastAbilityUse;
    }

    public int getFlameLevel() {
        return this.flameLevel;
    }

    public void setFlameLevel(int level) {
        this.flameLevel = level;
    }

    public boolean hasActiveFlame() {
        return this.flameType != null && !this.flameExtinguished;
    }

    public boolean canUseLevel2Abilities() {
        return this.hasActiveFlame() && this.upgradeLevel >= 2;
    }

    public int getFlameHealth() {
        return Math.max(0, 3 - this.deaths);
    }
}
