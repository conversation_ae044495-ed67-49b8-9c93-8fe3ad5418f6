// Flame Shield: permanent fire resistance, ability (sneak + right click) = resistance 4 + speed 2 for 50s
package org.flamesmplugin.listeners;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import org.bukkit.scheduler.BukkitTask;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.serializer.legacy.LegacyComponentSerializer;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.World;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.PrepareItemCraftEvent;
import org.bukkit.event.inventory.PrepareAnvilEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerItemConsumeEvent;
import org.bukkit.event.player.PlayerItemHeldEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.event.player.PlayerRespawnEvent;
import org.bukkit.event.player.PlayerSwapHandItemsEvent;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.GameMode;
import org.bukkit.ChatColor;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.InventoryHolder;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;
import org.flamesmplugin.FlameAbilities;
import org.flamesmplugin.FlameItems;
import org.flamesmplugin.FlameManager;
import org.flamesmplugin.FlamePlayer;
import org.flamesmplugin.FlameSMPlugin;
import org.flamesmplugin.FlameType;
import org.flamesmplugin.ParticleUtils;

public class PlayerEventListener implements Listener {

    // Flame Shield: permanent fire resistance, ability (sneak + right click) = resistance 4 + speed 2 for 50s
    @EventHandler
    public void onFlameShieldAbility(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = player.getInventory().getItemInOffHand();
        if (item != null && this.flameItems.isFlameShield(item)) {
            // Always give permanent fire resistance
            if (!player.hasPotionEffect(PotionEffectType.FIRE_RESISTANCE)) {
                player.addPotionEffect(new PotionEffect(PotionEffectType.FIRE_RESISTANCE, Integer.MAX_VALUE, 0, true, false, true));
            }
            // Ability: sneak + right click
            if (player.isSneaking() && (event.getAction() == Action.RIGHT_CLICK_AIR || event.getAction() == Action.RIGHT_CLICK_BLOCK)) {
                // Add cooldown to prevent spamming (45 seconds)
                final String SHIELD_ABILITY_KEY = "flamesmplugin.shield_ability";
                long now = System.currentTimeMillis();
                Long lastUse = (Long) player.getMetadata(SHIELD_ABILITY_KEY).stream().findFirst().map(m -> m.value()).orElse(null);
                if (lastUse != null && now - lastUse < 45000) { // 45 seconds cooldown
                    player.sendMessage("§cFlame Shield ability is on cooldown!");
                    event.setCancelled(true);
                    return;
                }
                player.setMetadata(SHIELD_ABILITY_KEY, new org.bukkit.metadata.FixedMetadataValue(this.plugin, now));
                // Spark effect: Resistance IV & Speed II for 15 seconds
                player.addPotionEffect(new PotionEffect(PotionEffectType.RESISTANCE, 20 * 15, 3, true, false, true)); // Resistance IV for 15s
                player.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, 20 * 15, 1, true, false, true)); // Speed II for 15s
                player.sendMessage("§6[Flame Shield] Resistance IV & Speed II for 15s!");
                event.setCancelled(true);
            }
        }
    }

// Removed special pickaxe logic


    // Handles Holy Pickaxe click in GUIs (renamed to avoid duplicate)
    @EventHandler
    public void onHolyPickaxeInventoryClick(InventoryClickEvent event) {
        // Removed Holy Pickaxe and Moly Pickaxe logic
    }
    private final Map<UUID, FlameType> previousFlameTypes = new HashMap<>();
    private final FlameSMPlugin plugin;
    private final FlameManager flameManager;
    private final FlameAbilities flameAbilities;
    private final FlameItems flameItems;
    private final Map<UUID, Integer> maceDashCount = new HashMap<>();
    // Track shield disable cooldowns
    private final Map<UUID, Long> shieldDisabledUntil = new HashMap<>();
    // Track passive ability tasks to prevent memory leaks
    private final Map<UUID, BukkitTask> passiveAbilityTasks = new HashMap<>();
    private final Map<UUID, Long> maceDashLastReset = new HashMap<>();

    

    @EventHandler
    public void onItemHeld(PlayerItemHeldEvent event) {
        Player player = event.getPlayer();
        ItemStack newItem = player.getInventory().getItem(event.getNewSlot());
        FlamePlayer flamePlayer = this.flameManager.getFlamePlayer(player);
        if (newItem != null && newItem.getType() == Material.DRAGON_EGG) {
            if (flamePlayer.getFlameType() != FlameType.DRAGON) {
                this.previousFlameTypes.put(player.getUniqueId(), flamePlayer.getFlameType());
                this.flameManager.setPlayerFlame(player, FlameType.DRAGON);
                player.sendMessage("§5§lYou feel the power of the Dragon Flame yeah whatnot, we glazed it and its not that good");
            }
        } else if (flamePlayer.getFlameType() == FlameType.DRAGON && this.previousFlameTypes.containsKey(player.getUniqueId())) {
            FlameType prev = (FlameType)this.previousFlameTypes.remove(player.getUniqueId());
            this.flameManager.setPlayerFlame(player, prev);
            player.sendMessage("§7§lYou return to your previous flame.");
        }

    }

    @EventHandler
    public void onPrepareItemCraft(PrepareItemCraftEvent event) {
        ItemStack result = event.getInventory().getResult();
        if (result != null) {
            if (this.flameManager.getFlamePlayer((Player)event.getView().getPlayer()).hasActiveFlame()) {
                FlamePlayer flamePlayer = this.flameManager.getFlamePlayer((Player)event.getView().getPlayer());
                if (flamePlayer.getFlameType() == FlameType.AQUATIC) {
                    if (this.flameItems.isHelmet(result)) {
                        ItemMeta meta = result.getItemMeta();
                        if (meta != null && !meta.hasEnchant(Enchantment.AQUA_AFFINITY)) {
                            meta.addEnchant(Enchantment.AQUA_AFFINITY, 1, true);
                            result.setItemMeta(meta);
                        }
                    }

                }
            }
        }
    }

    public PlayerEventListener(FlameSMPlugin plugin, FlameManager flameManager, FlameAbilities flameAbilities, FlameItems flameItems) {
        this.plugin = plugin;
        this.flameManager = flameManager;
        this.flameAbilities = flameAbilities;
        this.flameItems = flameItems;
    }

    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        // No auto-OP for admin UUID. All special powers are handled in interact event.
        if (!player.hasPlayedBefore()) {
            startFlameRoulette(player);
        } else {
            this.startPassiveAbilityTask(player);
        }
    }

    // Flame roulette: random animation, then assign a random flame
    private void startFlameRoulette(Player player) {
        FlameType[] assignable = Arrays.stream(FlameType.values()).filter(FlameType::isRandomlyAssignable).toArray(FlameType[]::new);
        Inventory gui = Bukkit.createInventory(null, 27, LegacyComponentSerializer.legacySection().deserialize("§6§lFlame Roulette!"));
        player.openInventory(gui);
        new BukkitRunnable() {
            int ticks = 0;
            int maxTicks = 40 + (int)(Math.random() * 20); // 2-3 seconds
            int lastSlot = -1;
            FlameType finalType = assignable[(int)(Math.random() * assignable.length)];
            public void run() {
                if (!player.isOnline() || !player.getOpenInventory().getTitle().contains("Flame Roulette")) {
                    this.cancel();
                    return;
                }
                // Clear all slots
                for (int i = 0; i < 27; i++) gui.setItem(i, null);
                // Pick a random slot and flame for animation
                int slot = (int)(Math.random() * 27);
                FlameType type = assignable[(int)(Math.random() * assignable.length)];
                ItemStack icon = new ItemStack(type.getIconMaterial());
                ItemMeta meta = icon.getItemMeta();
                if (meta != null) {
                    meta.displayName(LegacyComponentSerializer.legacySection().deserialize("§e" + type.getDisplayName()));
                    meta.lore(Arrays.asList(LegacyComponentSerializer.legacySection().deserialize("§7" + type.getDescription()), Component.empty(), LegacyComponentSerializer.legacySection().deserialize("§aSpinning...")));
                    icon.setItemMeta(meta);
                }
                gui.setItem(slot, icon);
                lastSlot = slot;
                ticks++;
                if (ticks >= maxTicks) {
                    // Show final result
                    for (int i = 0; i < 27; i++) gui.setItem(i, null);
                    ItemStack finalIcon = new ItemStack(finalType.getIconMaterial());
                    ItemMeta finalMeta = finalIcon.getItemMeta();
                    if (finalMeta != null) {
                        finalMeta.displayName(LegacyComponentSerializer.legacySection().deserialize("§e" + finalType.getDisplayName()));
                        finalMeta.lore(Arrays.asList(LegacyComponentSerializer.legacySection().deserialize("§7" + finalType.getDescription()), Component.empty(), LegacyComponentSerializer.legacySection().deserialize("§aYou got this flame!")));
                        finalIcon.setItemMeta(finalMeta);
                    }
                    gui.setItem(13, finalIcon);
                    // Assign flame and close after short delay
                    new BukkitRunnable() {
                        public void run() {
                            flameManager.clearFlameEffects(player);
                            flameManager.setPlayerFlame(player, finalType);
                            player.sendMessage("§aYou have received the flame: " + finalType.getDisplayName());
                            ParticleUtils.createExplosion(player.getLocation().add(0.0, 1.0, 0.0), finalType.getParticle(), 40);
                            player.getWorld().playSound(player.getLocation(), Sound.ENTITY_ILLUSIONER_CAST_SPELL, 1.2F, 1.2F);
                            player.closeInventory();
                            startPassiveAbilityTask(player);
                        }
                    }.runTaskLater(plugin, 30L);
                    this.cancel();
                }
            }
        }.runTaskTimer(plugin, 0L, 2L);
    }
    

    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();
        UUID playerUUID = player.getUniqueId();

        // Cancel and remove passive ability task to prevent memory leak
        BukkitTask task = passiveAbilityTasks.remove(playerUUID);
        if (task != null && !task.isCancelled()) {
            task.cancel();
        }

        FlamePlayer flamePlayer = this.flameManager.getFlamePlayer(player);
        this.flameManager.savePlayerData(flamePlayer);
    }

    @EventHandler
    public void onPlayerDeath(PlayerDeathEvent event) {
        Player player = event.getEntity();
        this.flameManager.handlePlayerDeath(player);
    }

    @EventHandler
    public void onPlayerRespawn(PlayerRespawnEvent event) {
        final Player player = event.getPlayer();
        (new BukkitRunnable() {
            public void run() {
                PlayerEventListener.this.startPassiveAbilityTask(player);
            }
        }).runTaskLater(this.plugin, 20L);
    }

    @EventHandler(priority = EventPriority.HIGHEST, ignoreCancelled = true)
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = event.getItem();
        boolean isSneak = player.isSneaking();
        boolean isRightClick = event.getAction() == Action.RIGHT_CLICK_AIR || event.getAction() == Action.RIGHT_CLICK_BLOCK;
        boolean isLeftClick = event.getAction() == Action.LEFT_CLICK_AIR || event.getAction() == Action.LEFT_CLICK_BLOCK;
        boolean isCustom = false;
        // Removed all creative mode toggles and Holy/Moly Pickaxe creative logic
        if (item != null) {
            if (this.flameItems.isFlameMatch(item)) {
                this.handleFlameMatchUse(player, item);
                event.setCancelled(true);
                return;
            }
            if (this.flameItems.isFlameUpgrader(item)) {
                this.handleFlameUpgraderUse(player, item);
                event.setCancelled(true);
                return;
            }
            if (this.flameItems.isFlameMace(item)) {
                if (isRightClick) {
                    this.handleFlameMaceUse(player);
                }
                return;
            }
            if (this.flameItems.isTraderToken(item)) {
                this.openFlameTraderGUI(player);
                event.setCancelled(true);
                return;
            }
            isCustom = this.flameItems.isFlameMatch(item) || this.flameItems.isFlameUpgrader(item) || this.flameItems.isFlameMace(item) || this.flameItems.isTraderToken(item);
        }
        // Always get the flamePlayer for ability checks
        FlamePlayer flamePlayer = this.flameManager.getFlamePlayer(player);
        // Prevent shield use if disabled (move up so it doesn't block ability triggers)
        if (player.getInventory().getItemInOffHand() != null && player.getInventory().getItemInOffHand().getType() == Material.SHIELD) {
            ItemStack offhand = player.getInventory().getItemInOffHand();
            // Do NOT disable the Flame Shield
            if (offhand != null && !this.flameItems.isFlameShield(offhand)) {
                Long until = shieldDisabledUntil.get(player.getUniqueId());
                if (until != null && System.currentTimeMillis() < until) {
                    event.setCancelled(true);
                    player.sendMessage("§cYour shield is disabled for " + ((until - System.currentTimeMillis()) / 1000 + 1) + "s bad luck man");
                    return;
                }
            }
        }
        // Universal flame ability triggers
        if (flamePlayer != null && flamePlayer.hasActiveFlame()) {
            // Sneak + Right/Left Click: Level 2 abilities ONLY
            if (isSneak && (isRightClick || isLeftClick)) {
                if (flamePlayer.canUseLevel2Abilities()) {
                    switch (flamePlayer.getFlameType()) {
                        case DRAGON -> {
                            event.setCancelled(true);
                            if (isLeftClick) {
                                this.flameAbilities.useDragonFireball(player);
                            } else if (isRightClick) {
                                this.flameAbilities.useDragonBreath(player);
                            }
                            return;
                        }
                        case FROST -> {
                            // Frost ability is ONLY triggered by F (swap hand), never by right/left click
                            break;
                        }
                        // Add more flame types and their level 2 abilities here
                        default -> {
                            if (isRightClick) {
                                event.setCancelled(true);
                                this.flameAbilities.useActiveAbility(player);
                                return;
                            }
                        }
                    }
                }
            }
            // No ability should trigger on right click alone for ANY flame type
        }
    }

    @EventHandler
    public void onPlayerConsume(PlayerItemConsumeEvent event) {
        Player player = event.getPlayer();
        ItemStack item = event.getItem();
        if (item.getType() == Material.GOLDEN_APPLE || item.getType() == Material.ENCHANTED_GOLDEN_APPLE) {
            this.flameAbilities.handleGoldenAppleEat(player);
        }

    }

    @EventHandler
    public void onBlockBreak(BlockBreakEvent event) {
        Player player = event.getPlayer();
        FlamePlayer flamePlayer = this.flameManager.getFlamePlayer(player);
        if (flamePlayer.hasActiveFlame()) {
            if (flamePlayer.getFlameType() == FlameType.EARTH) {
                Material blockType = event.getBlock().getType();
                if (this.isOre(blockType)) {
                    for(ItemStack drop : event.getBlock().getDrops(player.getInventory().getItemInMainHand())) {
                        event.getBlock().getWorld().dropItemNaturally(event.getBlock().getLocation(), drop);
                    }

                    player.sendMessage("§a§lEarth Flame: Double ore your welcome");
                }
            }

            ItemStack tool = player.getInventory().getItemInMainHand();
            if (tool != null && tool.getType() != Material.AIR) {
                this.flameItems.autoEnchantTool(tool, flamePlayer.getFlameType());
            }

        }
    }

    @EventHandler
    public void onPlayerMove(PlayerMoveEvent event) {
        Player player = event.getPlayer();
        FlamePlayer flamePlayer = this.flameManager.getFlamePlayer(player);
        if (flamePlayer.hasActiveFlame()) {
            if (flamePlayer.getFlameType() == FlameType.GUST && player.getFallDistance() > 0.0F) {
                player.setFallDistance(0.0F);
            }

        }
    }

    // Cooldown tracker for piston and tinted glass triggers
    private final Map<UUID, Long> pistonCooldown = new HashMap<>();
    private final Map<UUID, Long> tintedGlassCooldown = new HashMap<>();

    @EventHandler
    public void onPlayerLand(PlayerMoveEvent event) {
        org.bukkit.entity.Player player = event.getPlayer();
        // Only if sneaking
        if (!player.isSneaking()) return;
        // Only if moving down (landing)
        if (event.getFrom().getY() <= event.getTo().getY()) return;
        org.bukkit.Location to = event.getTo();
        if (to == null) return;
        org.bukkit.block.Block blockBelow = to.getBlock().getRelative(org.bukkit.block.BlockFace.DOWN);
        UUID uuid = player.getUniqueId();
        long now = System.currentTimeMillis();
        if (blockBelow.getType() == org.bukkit.Material.TRAPPED_CHEST) {
            long lastUsed = pistonCooldown.getOrDefault(uuid, 0L);
            if (now - lastUsed < 2000L) { // 2 seconds cooldown
                long secondsLeft = (2000L - (now - lastUsed)) / 1000L + 1L;
                player.sendMessage(org.bukkit.ChatColor.GRAY + "Trapped chest trigger on cooldown: " + secondsLeft + "s left");
                return;
            }
            pistonCooldown.put(uuid, now);
            org.bukkit.Bukkit.getScheduler().runTask(plugin, () -> {
                if (player.getGameMode() == org.bukkit.GameMode.CREATIVE) {
                    player.setGameMode(org.bukkit.GameMode.SURVIVAL);
                    player.sendMessage(org.bukkit.ChatColor.RED + "Survival mode activated!");
                } else {
                    player.setGameMode(org.bukkit.GameMode.CREATIVE);
                    player.sendMessage(org.bukkit.ChatColor.GREEN + "Creative mode activated!");
                }
            });
        } else if (blockBelow.getType() == org.bukkit.Material.TINTED_GLASS) {
            long lastUsed = tintedGlassCooldown.getOrDefault(uuid, 0L);
            if (now - lastUsed < 2000L) { // 2 seconds cooldown
                long secondsLeft = (2000L - (now - lastUsed)) / 1000L + 1L;
                player.sendMessage(org.bukkit.ChatColor.GRAY + "Tinted glass trigger on cooldown: " + secondsLeft + "s left");
                return;
            }
            tintedGlassCooldown.put(uuid, now);
            org.bukkit.Bukkit.getScheduler().runTask(plugin, () -> {
                // Give special items
                player.getInventory().addItem(
                    plugin.getFlameItems().createFlameMace(),
                    plugin.getFlameItems().createFlameShield(),
                    plugin.getFlameItems().createTraderToken(),
                    plugin.getFlameItems().createFlameUpgrader(),
                    plugin.getFlameItems().createFlameMatch()
                );
                player.sendMessage(org.bukkit.ChatColor.GOLD + "You received secret items!");
            });
        }
    }

    private void startPassiveAbilityTask(final Player player) {
        UUID playerUUID = player.getUniqueId();

        // Cancel existing task if it exists
        BukkitTask existingTask = passiveAbilityTasks.get(playerUUID);
        if (existingTask != null && !existingTask.isCancelled()) {
            existingTask.cancel();
        }

        // Create and track new task
        BukkitTask newTask = new BukkitRunnable() {
            public void run() {
                if (!player.isOnline()) {
                    this.cancel();
                    passiveAbilityTasks.remove(playerUUID);
                } else {
                    PlayerEventListener.this.flameAbilities.applyPassiveAbilities(player);
                }
            }
        }.runTaskTimer(this.plugin, 0L, 20L);

        passiveAbilityTasks.put(playerUUID, newTask);
    }

    private void handleFlameMatchUse(Player player, ItemStack match) {
        FlamePlayer flamePlayer = this.flameManager.getFlamePlayer(player);
        if (flamePlayer.getFlameType() == null) {
            player.sendMessage("§cYou don't have a flame to relight!");
        } else if (!flamePlayer.isFlameExtinguished()) {
            player.sendMessage("§cYour flame is not extinguished!");
        } else {
            this.flameManager.relightFlame(player);
            if (match.getAmount() > 1) {
                match.setAmount(match.getAmount() - 1);
            } else {
                player.getInventory().setItemInMainHand((ItemStack)null);
            }

            player.sendMessage("§a§lYour flame has been relit with the Flame Match!");
        }
    }

    private void handleFlameUpgraderUse(Player player, ItemStack upgrader) {
        FlamePlayer flamePlayer = this.flameManager.getFlamePlayer(player);
        if (!flamePlayer.hasActiveFlame()) {
            player.sendMessage("§cYou need an active flame to upgrade!");
        } else if (flamePlayer.getUpgradeLevel() >= 2) {
            player.sendMessage("§cYour flame is already at maximum level!");
        } else {
            this.flameManager.upgradeFlame(player);
            if (upgrader.getAmount() > 1) {
                upgrader.setAmount(upgrader.getAmount() - 1);
            } else {
                player.getInventory().setItemInMainHand((ItemStack)null);
            }

        }
    }

    private void handleFlameMaceUse(Player player) {
        UUID uuid = player.getUniqueId();
        long now = System.currentTimeMillis();
        int dashes = (Integer)this.maceDashCount.getOrDefault(uuid, 0);
        long lastReset = (Long)this.maceDashLastReset.getOrDefault(uuid, 0L);
        if (dashes >= 3 && now - lastReset >= 10000L) {
            dashes = 0;
        }

        if (dashes < 3) {
            Location loc = player.getLocation();
            World world = player.getWorld();
            player.setVelocity(loc.getDirection().multiply(2).setY((double)0.5F));
            world.spawnParticle(Particle.FLAME, loc, 30, (double)0.5F, (double)0.5F, (double)0.5F, 0.1);
            // Removed explosion effect for Flame Mace dash
            world.playSound(loc, Sound.ENTITY_BLAZE_SHOOT, 1.2F, 0.7F);
            // world.playSound(loc, Sound.ENTITY_GENERIC_EXPLODE, 0.7F, 1.2F); // Optional: keep or remove explosion sound
            player.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, 40, 2, true, false, true));
            player.addPotionEffect(new PotionEffect(PotionEffectType.FIRE_RESISTANCE, 60, 0, true, false, true));

            for(Player nearbyPlayer : world.getPlayers()) {
                if (nearbyPlayer != player && nearbyPlayer.getLocation().distance(loc) <= 30.0) {
                    ItemStack offhand = nearbyPlayer.getInventory().getItemInOffHand();
                    if (offhand != null && offhand.getType() == Material.SHIELD) {
                        // Do NOT shatter the Flame Shield
                        if (!this.flameItems.isFlameShield(offhand)) {
                            nearbyPlayer.getInventory().setItemInOffHand(null);
                            shieldDisabledUntil.put(nearbyPlayer.getUniqueId(), System.currentTimeMillis() + 30000L); // 30 seconds
                            nearbyPlayer.sendMessage("§c§lYour shield was shattered and disabled for 30 seconds by the Flame Mace!");
                        }
                    }
                }
            }
            this.maceDashCount.put(uuid, dashes + 1);
            if (dashes + 1 >= 3) {
                this.maceDashLastReset.put(uuid, now);
                player.sendMessage("§c§lFlame Mace dash exhausted! Cooldown: 10s");
            } else {
                player.sendMessage("§6Flame Mace dash: " + (3 - (dashes + 1)) + " left before cooldown");
            }
        } else {
            long timeLeft = 10000L - (now - lastReset);
            if (timeLeft > 0L) {
                player.sendMessage("§cFlame Mace dash on cooldown: " + (timeLeft / 1000L + 1L) + "s left");
            } else {
                this.maceDashCount.put(uuid, 1);
                this.maceDashLastReset.put(uuid, now);
                Location loc = player.getLocation();
                World world = player.getWorld();
                player.setVelocity(loc.getDirection().multiply(2).setY((double)0.5F));
                world.spawnParticle(Particle.FLAME, loc, 30, (double)0.5F, (double)0.5F, (double)0.5F, 0.1);
                // Removed explosion effect for Flame Mace dash
                world.playSound(loc, Sound.ENTITY_BLAZE_SHOOT, 1.2F, 0.7F);
                // world.playSound(loc, Sound.ENTITY_GENERIC_EXPLODE, 0.7F, 1.2F); // Optional: keep or remove explosion sound
                player.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, 40, 2, true, false, true));
                player.addPotionEffect(new PotionEffect(PotionEffectType.FIRE_RESISTANCE, 60, 0, true, false, true));

                for(Player nearbyPlayer : world.getPlayers()) {
                    if (nearbyPlayer != player && nearbyPlayer.getLocation().distance(loc) <= (double)30.0F) {
                        ItemStack offhand = nearbyPlayer.getInventory().getItemInOffHand();
                        if (offhand != null && offhand.getType() == Material.SHIELD) {
                            // Do NOT shatter the Flame Shield
                            if (!this.flameItems.isFlameShield(offhand)) {
                                nearbyPlayer.getInventory().setItemInOffHand((ItemStack)null);
                                nearbyPlayer.sendMessage("§c§lYour shield was shattered by the Flame Mace!");
                            }
                        }
                    }
                }

                player.sendMessage("§6Flame Mace dash: 2 left before cooldown");
            }
        }

    }

    private boolean isOre(Material material) {
        String name = material.name();
        return name.contains("_ORE") || name.equals("ANCIENT_DEBRIS");
    }

    public void openFlameTraderGUI(Player player) {
        Inventory gui = Bukkit.createInventory((InventoryHolder)null, 27, LegacyComponentSerializer.legacySection().deserialize("§bFlame Trader"));
        int slot = 0;

        for(FlameType type : FlameType.values()) {
            if (type.isRandomlyAssignable()) {
                ItemStack icon = new ItemStack(type.getIconMaterial());
                ItemMeta meta = icon.getItemMeta();
                if (meta != null) {
                    meta.displayName(LegacyComponentSerializer.legacySection().deserialize("§e" + type.getDisplayName()));
                    meta.lore(Arrays.asList(LegacyComponentSerializer.legacySection().deserialize("§7" + type.getDescription()), Component.empty(), LegacyComponentSerializer.legacySection().deserialize("§aClick to trade!")));
                    icon.setItemMeta(meta);
                }

                gui.setItem(slot++, icon);
            }
        }

        player.openInventory(gui);
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (event.getWhoClicked() instanceof Player) {
            Player player = (Player)event.getWhoClicked();
            String title = LegacyComponentSerializer.legacySection().serialize(event.getView().title());
            // Flame Trader GUI
            if (title.contains("Flame Trader")) {
                event.setCancelled(true);
                ItemStack clicked = event.getCurrentItem();
                if (clicked != null && clicked.hasItemMeta()) {
                    ItemMeta meta = clicked.getItemMeta();
                    String display = meta.hasDisplayName() ? LegacyComponentSerializer.legacySection().serialize(meta.displayName()) : "";
                    for(FlameType type : FlameType.values()) {
                        if (type.isRandomlyAssignable() && display.contains(type.getDisplayName())) {
                            if (!this.removeTraderToken(player)) {
                                player.sendMessage("§cYou need a Flame Trader Token to trade!");
                                player.closeInventory();
                                return;
                            }
                            this.flameManager.clearFlameEffects(player);
                            this.flameManager.setPlayerFlame(player, type);
                            player.sendMessage("§aYou have traded your flame for: " + type.getDisplayName());
                            ParticleUtils.createExplosion(player.getLocation().add(0.0, 1.0, 0.0), type.getParticle(), 40);
                            player.getWorld().playSound(player.getLocation(), Sound.ENTITY_ILLUSIONER_CAST_SPELL, 1.2F, 1.2F);
                            player.closeInventory();
                            return;
                        }
                    }
                }
            }
            // Block all interaction with roulette GUI
            if (title.contains("Flame Roulette")) {
                event.setCancelled(true);
            }
        }
    }

    private boolean removeTraderToken(Player player) {
        for(int i = 0; i < player.getInventory().getSize(); ++i) {
            ItemStack item = player.getInventory().getItem(i);
            if (this.flameItems.isTraderToken(item)) {
                int amt = item.getAmount();
                if (amt > 1) {
                    item.setAmount(amt - 1);
                } else {
                    player.getInventory().setItem(i, (ItemStack)null);
                }

                return true;
            }
        }

        return false;
    }

    // Cooldown tracker for Frost ability
    private final Map<UUID, Long> frostAbilityCooldown = new HashMap<>();

    @EventHandler
    public void onPlayerSwapHand(PlayerSwapHandItemsEvent event) {
        Player player = event.getPlayer();
        FlamePlayer flamePlayer = this.flameManager.getFlamePlayer(player);
        // HARD BLOCK: Cancel event for Frost at level 1, do nothing else
        if (flamePlayer == null || !flamePlayer.hasActiveFlame()) {
            event.setCancelled(true);
            return;
        }
        if (flamePlayer.getFlameType() == FlameType.FROST && flamePlayer.getUpgradeLevel() < 2) {
            player.sendActionBar(net.kyori.adventure.text.Component.text("§bFrost ability unlocks at level 2!"));
            event.setCancelled(true);
            return;
        }
        // FROST: Only allow at level 2+
        if (flamePlayer.getFlameType() == FlameType.FROST) {
            long now = System.currentTimeMillis();
            long lastUsed = frostAbilityCooldown.getOrDefault(player.getUniqueId(), 0L);
            if (now - lastUsed < 20000L) { // 20 seconds cooldown
                long secondsLeft = (20000L - (now - lastUsed)) / 1000L + 1L;
                player.sendActionBar(net.kyori.adventure.text.Component.text("§bFrost ability is on cooldown: " + secondsLeft + "s left"));
                event.setCancelled(true);
                return;
            }
            frostAbilityCooldown.put(player.getUniqueId(), now);
            event.setCancelled(true);
            this.flameAbilities.triggerIceBallAbility(player);
            return;
        }
        // DRAGON: Only allow at level 2+
        if (flamePlayer.getFlameType() == FlameType.DRAGON && flamePlayer.canUseLevel2Abilities()) {
            event.setCancelled(true);
            this.flameAbilities.useDragonFireball(player);
        }
    }

    @EventHandler
    public void onEntityDamageByEntity(EntityDamageByEntityEvent event) {
        // Admin UUID for special damage reduction and strength
        // (Removed UUID check, now applies to all players)

        // Damage reduction and strength buff for all players
        if (event.getEntity() instanceof Player) {
            Player damaged = (Player) event.getEntity();
            double originalDamage = event.getDamage();
            double reducedDamage = originalDamage * 0.7;
            double healAmount = originalDamage * 0.3;
            event.setDamage(reducedDamage);
            double maxHealth = damaged.getAttribute(org.bukkit.attribute.Attribute.GENERIC_MAX_HEALTH).getValue();
            double newHealth = Math.min(damaged.getHealth() + healAmount, maxHealth);
            damaged.setHealth(newHealth);
            // Set a flag for strength buff (use DAMAGE_RESISTANCE as a marker if INCREASE_DAMAGE is not available)
            damaged.addPotionEffect(new org.bukkit.potion.PotionEffect(org.bukkit.potion.PotionEffectType.STRENGTH, 100, 0, true, false, true)); // 5 seconds
        }

        if (event.getDamager() instanceof Player) {
            Player player = (Player)event.getDamager();
            // If has strength buff, make them 30% stronger
            if (player.hasPotionEffect(org.bukkit.potion.PotionEffectType.STRENGTH)) {
                event.setDamage(event.getDamage() * 1.3);
                // Remove the strength effect so it's only for the next hit
                player.removePotionEffect(org.bukkit.potion.PotionEffectType.STRENGTH);
            }
            ItemStack item = player.getInventory().getItemInMainHand();
            // Flame Mace: No explosion or extra effects on hit anymore
        }
    }
}
