//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.flamesmplugin.commands;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.serializer.legacy.LegacyComponentSerializer;
import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.Plugin;
import org.bukkit.plugin.RegisteredListener;
import org.bukkit.plugin.java.JavaPlugin;
import org.flamesmplugin.FlameItems;
import org.flamesmplugin.FlameManager;
import org.flamesmplugin.FlamePlayer;
import org.flamesmplugin.FlameSMPlugin;
import org.flamesmplugin.FlameType;
import org.flamesmplugin.MessageUtils;
import org.flamesmplugin.listeners.PlayerEventListener;

public class FlameCommand implements CommandExecutor, TabCompleter {
    private final FlameManager flameManager;
    private final FlameItems flameItems;

    public FlameCommand(FlameManager flameManager, FlameItems flameItems) {
        this.flameManager = flameManager;
        this.flameItems = flameItems;
    }

    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (args.length == 0) {
            this.sendHelp(sender);
            return true;
        } else {
            switch (args[0].toLowerCase()) {
                case "info":
                    return this.handleInfo(sender, args);
                case "set":
                    return this.handleSet(sender, args);
                case "upgrade":
                    return this.handleUpgrade(sender, args);
                case "relight":
                    return this.handleRelight(sender, args);
                case "give":
                    return this.handleGive(sender, args);
                case "reset":
                    return this.handleReset(sender, args);
                case "list":
                    return this.handleList(sender);
                case "trade":
                case "flametrade":
                    if (!(sender instanceof Player)) {
                        sender.sendMessage("Only players can trade flames.");
                        return true;
                    } else {
                        Player player = (Player)sender;
                        if (args.length < 2) {
                            sender.sendMessage("§cUsage: /flametrade <flameType>");
                            return true;
                        } else {
                            String requested = args[1].toUpperCase();

                            try {
                                FlameType requestedType = FlameType.valueOf(requested);
                                if (!requestedType.isRandomlyAssignable()) {
                                    sender.sendMessage("§cYou cannot trade for this flame type.");
                                    return true;
                                }

                                FlamePlayer flamePlayer = this.flameManager.getFlamePlayer(player);
                                if (flamePlayer.getFlameType() == requestedType) {
                                    sender.sendMessage("§eYou already have this flame type.");
                                    return true;
                                }

                                this.flameManager.setPlayerFlame(player, requestedType);
                                sender.sendMessage("§aYou have traded your flame for: " + requestedType.getDisplayName());
                            } catch (IllegalArgumentException var16) {
                                sender.sendMessage("§cInvalid flame type. Available: " + Arrays.toString(FlameType.values()));
                            }

                            return true;
                        }
                    }
                case "trader":
                case "flametrader":
                    if (!(sender instanceof Player)) {
                        sender.sendMessage("Only players can use the trader GUI.");
                        return true;
                    }

                    Player guiPlayer = (Player)sender;
                    PlayerEventListener listener = null;
                    Plugin plugin = JavaPlugin.getPlugin(FlameSMPlugin.class);

                    for(RegisteredListener rl : HandlerList.getRegisteredListeners(plugin)) {
                        Listener l = rl.getListener();
                        if (l instanceof PlayerEventListener) {
                            listener = (PlayerEventListener)l;
                            break;
                        }
                    }

                    if (listener != null) {
                        listener.openFlameTraderGUI(guiPlayer);
                    } else {
                        guiPlayer.sendMessage("§cCould not open trader GUI. Try right-clicking the token.");
                    }

                    return true;
                case "recipe":
                case "flamerecipe":
                    return this.handleRecipe(sender, args);
                case "wardenboss":
                    return (new WardenBossCommand()).onCommand(sender, command, label, (String[])Arrays.copyOfRange(args, 1, args.length));
                default:
                    this.sendHelp(sender);
                    return true;
            }
        }
    }

    private boolean handleInfo(CommandSender sender, String[] args) {
        Player target;
        if (args.length > 1) {
            if (!sender.hasPermission("flame.admin")) {
                MessageUtils.sendActionBar((Player)sender, "No permission.");
                return true;
            }

            target = Bukkit.getPlayer(args[1]);
            if (target == null) {
                MessageUtils.sendActionBar((Player)sender, "Player not found.");
                return true;
            }
        } else {
            if (!(sender instanceof Player)) {
                sender.sendMessage("§cConsole must specify a player!");
                return true;
            }

            target = (Player)sender;
        }

        FlamePlayer flamePlayer = this.flameManager.getFlamePlayer(target);
        if (flamePlayer.getFlameType() == null) {
            MessageUtils.sendTitle(target, "No flame assigned", (String)null);
        } else {
            StringBuilder info = new StringBuilder();
            info.append(flamePlayer.getFlameType().getDisplayName()).append(" | Level: ").append(flamePlayer.getUpgradeLevel()).append(" | Health: ").append(flamePlayer.getFlameHealth()).append("/3");
            MessageUtils.sendTitle(target, "Flame Info", info.toString());
        }

        return true;
    }

    private boolean handleSet(CommandSender sender, String[] args) {
        if (!sender.hasPermission("flame.admin")) {
            sender.sendMessage("§cYou don't have permission to use this command!");
            return true;
        } else if (args.length < 3) {
            sender.sendMessage("§cUsage: /flame set <player> <flame_type>");
            return true;
        } else {
            Player target = Bukkit.getPlayer(args[1]);
            if (target == null) {
                sender.sendMessage("§cPlayer not found!");
                return true;
            } else {
                try {
                    FlameType flameType = FlameType.valueOf(args[2].toUpperCase());
                    FlamePlayer flamePlayer = this.flameManager.getFlamePlayer(target);
                    flamePlayer.setFlameType(flameType);
                    flamePlayer.setFlameExtinguished(false);
                    flamePlayer.resetDeaths();
                    this.flameManager.savePlayerData(flamePlayer);
                    String var10001 = target.getName();
                    sender.sendMessage("§aSet " + var10001 + "'s flame to " + flameType.getDisplayName());
                    target.sendMessage("§6Your flame has been set to " + flameType.getDisplayName() + "!");
                } catch (IllegalArgumentException var10) {
                    StringBuilder validTypes = new StringBuilder();

                    for(FlameType type : FlameType.values()) {
                        if (validTypes.length() > 0) {
                            validTypes.append(", ");
                        }

                        validTypes.append(type.name().toLowerCase());
                    }

                    sender.sendMessage("§cInvalid flame type! Valid types: " + validTypes.toString());
                }

                return true;
            }
        }
    }

    private boolean handleUpgrade(CommandSender sender, String[] args) {
        if (!sender.hasPermission("flame.admin")) {
            sender.sendMessage("§cYou don't have permission to use this command!");
            return true;
        } else {
            Player target;
            if (args.length > 1) {
                target = Bukkit.getPlayer(args[1]);
                if (target == null) {
                    sender.sendMessage("§cPlayer not found!");
                    return true;
                }
            } else {
                if (!(sender instanceof Player)) {
                    sender.sendMessage("§cConsole must specify a player!");
                    return true;
                }

                target = (Player)sender;
            }

            this.flameManager.upgradeFlame(target);
            sender.sendMessage("§aUpgraded " + target.getName() + "'s flame to Level 2!");
            return true;
        }
    }

    private boolean handleRelight(CommandSender sender, String[] args) {
        if (!sender.hasPermission("flame.admin")) {
            sender.sendMessage("§cYou don't have permission to use this command!");
            return true;
        } else {
            Player target;
            if (args.length > 1) {
                target = Bukkit.getPlayer(args[1]);
                if (target == null) {
                    sender.sendMessage("§cPlayer not found!");
                    return true;
                }
            } else {
                if (!(sender instanceof Player)) {
                    sender.sendMessage("§cConsole must specify a player!");
                    return true;
                }

                target = (Player)sender;
            }

            this.flameManager.relightFlame(target);
            sender.sendMessage("§aRelit " + target.getName() + "'s flame!");
            return true;
        }
    }

    private boolean handleGive(CommandSender sender, String[] args) {
        if (!sender.hasPermission("flame.admin")) {
            sender.sendMessage("§cYou don't have permission to use this command!");
            return true;
        } else if (args.length < 3) {
            sender.sendMessage("§cUsage: /flame give <player> <item>");
            sender.sendMessage("§cItems: match, mace, upgrader, shield, life");
            return true;
        } else {
            Player target = Bukkit.getPlayer(args[1]);
            if (target == null) {
                sender.sendMessage("§cPlayer not found!");
                return true;
            } else {
                ItemStack item;
                switch (args[2].toLowerCase()) {
                    case "match":
                        item = this.flameItems.createFlameMatch();
                        break;
                    case "mace":
                        item = this.flameItems.createFlameMace();
                        break;
                    case "upgrader":
                        item = this.flameItems.createFlameUpgrader();
                        break;
                    case "shield":
                        item = this.flameItems.createFlameShield();
                        break;
                    case "life":
                        item = this.flameItems.createFlameLife();
                        break;
                    default:
                        sender.sendMessage("§cInvalid item type! Valid types: match, mace, upgrader, shield, life");
                        return true;
                }

                target.getInventory().addItem(new ItemStack[]{item});
                String var10001 = target.getName();
                sender.sendMessage("§aGave " + var10001 + " a " + args[2] + "!");
                if (item.getItemMeta() != null && item.getItemMeta().hasDisplayName()) {
                    Component displayName = item.getItemMeta().displayName();
                    String legacyName = LegacyComponentSerializer.legacySection().serialize(displayName);
                    target.sendMessage("§6You received a " + legacyName + "!");
                } else {
                    target.sendMessage("§6You received a " + args[2] + "!");
                }

                return true;
            }
        }
    }

    private boolean handleReset(CommandSender sender, String[] args) {
        if (!sender.hasPermission("flame.admin")) {
            sender.sendMessage("§cYou don't have permission to use this command!");
            return true;
        } else if (args.length < 2) {
            sender.sendMessage("§cUsage: /flame reset <player>");
            return true;
        } else {
            Player target = Bukkit.getPlayer(args[1]);
            if (target == null) {
                sender.sendMessage("§cPlayer not found!");
                return true;
            } else {
                FlamePlayer flamePlayer = this.flameManager.getFlamePlayer(target);
                flamePlayer.setFlameType((FlameType)null);
                flamePlayer.setUpgradeLevel(1);
                flamePlayer.resetDeaths();
                flamePlayer.setFlameExtinguished(false);
                this.flameManager.savePlayerData(flamePlayer);
                sender.sendMessage("§aReset " + target.getName() + "'s flame data!");
                target.sendMessage("§cYour flame data has been reset!");
                return true;
            }
        }
    }

    private boolean handleList(CommandSender sender) {
        if (sender instanceof Player player) {
            StringBuilder list = new StringBuilder();

            for(FlameType flameType : FlameType.values()) {
                list.append(flameType.getDisplayName()).append(": ").append(flameType.getDescription()).append("\n");
            }

            MessageUtils.sendTitle(player, "Flame Types", list.toString());
        } else {
            sender.sendMessage("=== Available Flame Types ===");

            for(FlameType flameType : FlameType.values()) {
                sender.sendMessage("- " + flameType.getDisplayName());
                sender.sendMessage("  " + flameType.getDescription());
            }
        }

        return true;
    }

    private boolean handleRecipe(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sender.sendMessage("§cUsage: /flamerecipe <item>");
            sender.sendMessage("§7Items: match, mace, upgrader, trader");
            return true;
        } else {
            switch (args[1].toLowerCase()) {
                case "match":
                    sender.sendMessage("§6Flame Match Recipe:");
                    sender.sendMessage("§7Blaze Powder | Stick | Blaze Powder");
                    sender.sendMessage("§7Blaze Powder | Torch | Blaze Powder");
                    sender.sendMessage("§7Blaze Powder | Stick | Blaze Powder");
                    break;
                case "mace":
                    sender.sendMessage("§6Flame Mace Recipe:");
                    sender.sendMessage("§7Netherite Ingot | Blaze Rod | Netherite Ingot");
                    sender.sendMessage("§7Blaze Rod | Netherite Sword | Blaze Rod");
                    sender.sendMessage("§7Netherite Ingot | Blaze Rod | Netherite Ingot");
                    break;
                case "upgrader":
                    sender.sendMessage("§6Flame Upgrader Recipe:");
                    sender.sendMessage("§7Blaze Powder | Blaze Powder | Blaze Powder");
                    sender.sendMessage("§7Blaze Powder | Nether Star | Blaze Powder");
                    sender.sendMessage("§7Blaze Powder | Blaze Powder | Blaze Powder");
                    break;
                case "trader":
                    sender.sendMessage("§6Flame Trader Token Recipe:");
                    sender.sendMessage("§7Diamond Block | Totem of Undying | Diamond Block");
                    sender.sendMessage("§7Netherite Scrap | Nether Star | Netherite Scrap");
                    sender.sendMessage("§7Diamond Block | Enchanted Golden Apple | Diamond Block");
                    break;
                default:
                    sender.sendMessage("§cUnknown item! Valid: match, mace, upgrader, trader");
            }

            return true;
        }
    }

    private void sendHelp(CommandSender sender) {
        sender.sendMessage("§6=== Flame Commands ===");
        sender.sendMessage("§7/flame info [player] - Show flame information");
        sender.sendMessage("§7/flame list - List all flame types");
        if (sender.hasPermission("flame.admin")) {
            sender.sendMessage("§c=== Admin Commands ===");
            sender.sendMessage("§7/flame set <player> <type> - Set player's flame");
            sender.sendMessage("§7/flame upgrade [player] - Upgrade flame to Level 2");
            sender.sendMessage("§7/flame relight [player] - Relight extinguished flame");
            sender.sendMessage("§7/flame give <player> <item> - Give custom items");
            sender.sendMessage("§7/flame reset <player> - Reset player's flame data");
        }

    }

    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();
        if (args.length == 1) {
            completions.addAll(Arrays.asList("info", "list"));
            if (sender.hasPermission("flame.admin")) {
                completions.addAll(Arrays.asList("set", "upgrade", "relight", "give", "reset"));
            }
        } else if (args.length == 2) {
            if (args[0].equalsIgnoreCase("set") || args[0].equalsIgnoreCase("upgrade") || args[0].equalsIgnoreCase("relight") || args[0].equalsIgnoreCase("give") || args[0].equalsIgnoreCase("reset") || args[0].equalsIgnoreCase("info")) {
                for(Player player : Bukkit.getOnlinePlayers()) {
                    completions.add(player.getName());
                }
            }
        } else if (args.length == 3) {
            if (args[0].equalsIgnoreCase("set")) {
                for(FlameType flameType : FlameType.values()) {
                    completions.add(flameType.name().toLowerCase());
                }
            } else if (args[0].equalsIgnoreCase("give")) {
                completions.addAll(Arrays.asList("match", "mace", "upgrader"));
            }
        }

        return completions;
    }
}
