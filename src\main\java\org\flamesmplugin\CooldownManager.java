//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.flamesmplugin;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import org.bukkit.entity.Player;

public class CooldownManager {
    private final Map<UUID, Long> cooldowns = new HashMap<>();

    public void setCooldown(Player player, int seconds) {
        this.setCooldown(player.getUniqueId(), seconds);
    }

    public void setCooldown(UUID playerId, int seconds) {
        long cooldownTime = System.currentTimeMillis() + (long)seconds * 1000L;
        this.cooldowns.put(playerId, cooldownTime);
    }

    public boolean isOnCooldown(Player player) {
        return this.isOnCooldown(player.getUniqueId());
    }

    public boolean isOnCooldown(UUID playerId) {
        Long cooldownTime = (Long)this.cooldowns.get(playerId);
        if (cooldownTime == null) {
            return false;
        } else if (System.currentTimeMillis() >= cooldownTime) {
            this.cooldowns.remove(playerId);
            return false;
        } else {
            return true;
        }
    }

    public int getRemainingCooldown(Player player) {
        return this.getRemainingCooldown(player.getUniqueId());
    }

    public int getRemainingCooldown(UUID playerId) {
        Long cooldownTime = (Long)this.cooldowns.get(playerId);
        if (cooldownTime == null) {
            return 0;
        } else {
            long remaining = cooldownTime - System.currentTimeMillis();
            if (remaining <= 0L) {
                this.cooldowns.remove(playerId);
                return 0;
            } else {
                return (int)Math.ceil((double)remaining / (double)1000.0F);
            }
        }
    }

    public void removeCooldown(Player player) {
        this.removeCooldown(player.getUniqueId());
    }

    public void removeCooldown(UUID playerId) {
        this.cooldowns.remove(playerId);
    }

    public static int getCooldownForFlame(FlameType flameType) {
        // All flame types have a 30 second cooldown for active abilities
        return 30;
    }
}
