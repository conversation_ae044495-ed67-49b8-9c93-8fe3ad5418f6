//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.flamesmplugin;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.serializer.legacy.LegacyComponentSerializer;
import org.bukkit.Bukkit;
import org.bukkit.attribute.Attribute;
import org.bukkit.attribute.AttributeInstance;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffectType;

public class FlameManager {
    private final FlameSMPlugin plugin;
    private final Map<UUID, FlamePlayer> flamePlayers;
    private final File dataFile;
    private FileConfiguration dataConfig;

    public FlameManager(FlameSMPlugin plugin) {
        this.plugin = plugin;
        this.flamePlayers = new HashMap<>();
        this.dataFile = new File(plugin.getDataFolder(), "playerdata.yml");
        this.setupDataFile();
        this.loadData();
    }

    private void setupDataFile() {
        if (!this.plugin.getDataFolder().exists()) {
            this.plugin.getDataFolder().mkdirs();
        }

        if (!this.dataFile.exists()) {
            try {
                if (!this.dataFile.createNewFile()) {
                    this.plugin.getLogger().log(java.util.logging.Level.WARNING, "Could not create playerdata.yml file!");
                }
            } catch (IOException e) {
                this.plugin.getLogger().log(java.util.logging.Level.SEVERE, "Could not create playerdata.yml file!", e);
            }
        }

        this.dataConfig = YamlConfiguration.loadConfiguration(this.dataFile);
    }

    public FlamePlayer getFlamePlayer(Player player) {
        return this.getFlamePlayer(player.getUniqueId());
    }

    public FlamePlayer getFlamePlayer(UUID playerId) {
        return (FlamePlayer)this.flamePlayers.computeIfAbsent(playerId, FlamePlayer::new);
    }

    public void assignRandomFlame(Player player) {
        FlamePlayer flamePlayer = this.getFlamePlayer(player);
        if (flamePlayer.getFlameType() == null) {
            FlameType randomFlame = FlameType.getRandomFlame();
            flamePlayer.setFlameType(randomFlame);
            player.sendMessage("§6You have been blessed with the " + randomFlame.getDisplayName() + "!");
            this.savePlayerData(flamePlayer);
        }

    }

    public void handlePlayerDeath(Player player) {
        FlamePlayer flamePlayer = this.getFlamePlayer(player);
        if (flamePlayer.hasActiveFlame()) {
            flamePlayer.addDeath();
            if (flamePlayer.isFlameExtinguished()) {
                player.sendMessage("§c§lYour flame has been extinguished! Find a match to relight it.");
                Component msg = LegacyComponentSerializer.legacySection().deserialize("§c" + player.getName() + "'s flame has been extinguished!");
                Bukkit.getServer().sendMessage(msg);
            } else {
                int health = flamePlayer.getFlameHealth();
                player.sendMessage("§e§lYour flame has been damaged! Flame health: " + health + "/3");
            }

            this.savePlayerData(flamePlayer);
        }

    }

    public void upgradeFlame(Player player) {
        FlamePlayer flamePlayer = this.getFlamePlayer(player);
        if (flamePlayer.hasActiveFlame() && flamePlayer.getUpgradeLevel() < 2) {
            flamePlayer.setUpgradeLevel(2);
            MessageUtils.sendTitle(player, "Flame upgraded!", "Level 2 abilities unlocked");
            this.savePlayerData(flamePlayer);
        }

    }

    public void relightFlame(Player player) {
        FlamePlayer flamePlayer = this.getFlamePlayer(player);
        if (flamePlayer.getFlameType() != null && flamePlayer.isFlameExtinguished()) {
            flamePlayer.relightFlame();
            player.sendMessage("§a§lYour flame has been relit Succesfully, dont die again.");
            this.savePlayerData(flamePlayer);
        }

    }

    public void setPlayerFlame(Player player, FlameType type) {
        FlamePlayer flamePlayer = this.getFlamePlayer(player);
        FlameType oldType = flamePlayer.getFlameType();
        // If leaving Life, remove extra hearts
        if (oldType == FlameType.LIFE && type != FlameType.LIFE) {
            clearFlameEffects(player);
        }
        flamePlayer.setFlameType(type);
        // If switching to Life, add extra hearts
        if (type == FlameType.LIFE) {
            org.bukkit.attribute.AttributeInstance healthAttr = player.getAttribute(org.bukkit.attribute.Attribute.GENERIC_MAX_HEALTH);
            if (healthAttr != null) {
                // Add 5 extra hearts (10 health)
                healthAttr.setBaseValue(30.0);
                if (player.getHealth() > 30.0) player.setHealth(30.0);
            }
            player.setAbsorptionAmount(0.0);
        }
        this.savePlayerData(flamePlayer);
    }

    // Set a player's flame level (for SilentOps integration)
    public void setFlameLevel(Player player, int level) {
        FlamePlayer flamePlayer = this.getFlamePlayer(player);
        flamePlayer.setFlameLevel(level);
        this.savePlayerData(flamePlayer);
    }

    public void savePlayerData(FlamePlayer flamePlayer) {
        String path = "players." + flamePlayer.getPlayerId().toString();
        if (flamePlayer.getFlameType() != null) {
            this.dataConfig.set(path + ".flame", flamePlayer.getFlameType().name());
            this.dataConfig.set(path + ".upgrade", flamePlayer.getUpgradeLevel());
            this.dataConfig.set(path + ".deaths", flamePlayer.getDeaths());
            this.dataConfig.set(path + ".extinguished", flamePlayer.isFlameExtinguished());
        }

        this.saveData();
    }

    public void loadData() {
        if (this.dataConfig.getConfigurationSection("players") != null) {
            for(String uuidString : this.dataConfig.getConfigurationSection("players").getKeys(false)) {
                try {
                    UUID playerId = UUID.fromString(uuidString);
                    String path = "players." + uuidString;
                    String flameName = this.dataConfig.getString(path + ".flame");
                    if (flameName != null) {
                        FlameType flameType = FlameType.valueOf(flameName);
                        int upgrade = this.dataConfig.getInt(path + ".upgrade", 1);
                        int deaths = this.dataConfig.getInt(path + ".deaths", 0);
                        boolean extinguished = this.dataConfig.getBoolean(path + ".extinguished", false);
                        FlamePlayer flamePlayer = new FlamePlayer(playerId, flameType, upgrade, deaths, extinguished);
                        this.flamePlayers.put(playerId, flamePlayer);
                    }
                } catch (Exception var11) {
                    this.plugin.getLogger().log(java.util.logging.Level.WARNING, "Failed to load data for player: " + uuidString, var11);
                }
            }

        }
    }

    public void saveData() {
        try {
            this.dataConfig.save(this.dataFile);
        } catch (IOException e) {
            this.plugin.getLogger().log(java.util.logging.Level.SEVERE, "Could not save playerdata.yml!", e);
        }

    }

    public void saveAllData() {
        for(FlamePlayer flamePlayer : this.flamePlayers.values()) {
            this.savePlayerData(flamePlayer);
        }

    }

    public void clearFlameEffects(Player player) {
        AttributeInstance healthAttr = player.getAttribute(Attribute.GENERIC_MAX_HEALTH);
        if (healthAttr != null) {
            // Remove only max health modifiers that were added by the plugin (Life flame), not all modifiers
            // Remove all modifiers with a known plugin namespace or name, or fallback to removing all if not possible
            // Future-proof: remove all modifiers with name containing "LifeFlame" or "FlamePlugin" (case-insensitive)
            healthAttr.getModifiers().stream()
                .filter(mod -> mod.getName().toLowerCase().contains("lifeflame") || mod.getName().toLowerCase().contains("flameplugin"))
                .forEach(healthAttr::removeModifier);

            // Always set max health to 20, and clamp current health
            healthAttr.setBaseValue(20.0);
            if (player.getHealth() > 20.0) {
                player.setHealth(20.0);
            }
        }
        // Remove only absorption effect, not regeneration (so golden apple regen is preserved)
        player.setAbsorptionAmount(0.0);
        player.removePotionEffect(PotionEffectType.ABSORPTION);
        // Do NOT remove regeneration effect here (fixes golden apple bug)
    }
}
