# FlameSMPlugin Configuration

# General Settings
general:
  # Whether to assign random flames to new players
  auto-assign-flames: true
  
  # Whether to broadcast when a player's flame is extinguished
  broadcast-extinguish: true
  
  # Whether to show particle effects
  show-particles: true

# Flame Settings
flames:
  # Number of deaths before flame is extinguished
  max-deaths: 3
  
  # Whether Dragon Flame can be randomly assigned (should be false for boss reward)
  dragon-random-assign: false

# Ability Cooldowns (in seconds)
cooldowns:
  frost-barrage: 90
  earth-slam: 60
  tidal-wave: 90
  sky-slam: 75
  cosmic-barrage: 75
  shadow-form: 90
  life-bubble: 60
  dragon-power: 60

# Combat Settings
combat:
  # Burning Flame damage bonus percentage
  burning-damage-bonus: 50
  
  # Frost Flame freeze chance percentage
  frost-freeze-chance: 30
  
  # Shadow Flame blind chance percentage
  shadow-blind-chance: 40

# Custom Items
items:
  # Whether custom items are enabled
  enabled: true
  
  # Flame Mace dash distance multiplier
  mace-dash-power: 2.0
  
  # Flame Mace shield break radius
  mace-shield-radius: 30

# Messages
messages:
  flame-assigned: "&6You have been assigned the %flame%"
  flame-extinguished: "&cYour flame has been extinguished. Find a match to relight it."
  flame-damaged: "&eYour flame has been damaged. Flame health: %health%/3"
  flame-relit: "&aYour flame has been relit."
  flame-upgraded: "&aYour flame has been upgraded. Level 2 abilities are now available."
  no-permission: "&cYou don't have permission to use this command."
  player-not-found: "&cPlayer not found."
  cooldown-message: "&cAbility on cooldown. %time% seconds remaining."
