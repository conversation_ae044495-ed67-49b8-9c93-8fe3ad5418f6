package org.flamesmplugin.listeners;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.AsyncPlayerChatEvent;

public class <PERSON><PERSON><PERSON><PERSON><PERSON> implements Listener {
    @EventHandler
    public void onChat(AsyncPlayerChatEvent event) {
        event.setCancelled(true); // Cancel the original (signed) chat
        String format = ChatColor.GRAY + "<" + event.getPlayer().getName() + "> " + ChatColor.WHITE + event.getMessage();
        for (Player p : Bukkit.getOnlinePlayers()) {
            p.sendMessage(format);
        }
    }
}
