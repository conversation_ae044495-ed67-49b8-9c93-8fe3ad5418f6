//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.flamesmplugin.commands;

import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.flamesmplugin.FlameManager;
import org.flamesmplugin.FlamePlayer;

public class ResetFlameLevelCommand implements CommandExecutor {
    private final FlameManager flameManager;

    public ResetFlameLevelCommand(FlameManager flameManager) {
        this.flameManager = flameManager;
    }

    public boolean onCommand(CommandSender sender, Command cmd, String label, String[] args) {
        if (!(sender instanceof Player player)) {
            sender.sendMessage("§cOnly players can use this command.");
            return true;
        }
        FlamePlayer flamePlayer = this.flameManager.getFlamePlayer(player);
        if (flamePlayer == null) {
            player.sendMessage("§cYou do not have a flame profile.");
            return true;
        }
        flamePlayer.setFlameLevel(0);
        flamePlayer.setUpgradeLevel(1);
        flamePlayer.resetDeaths();
        flamePlayer.setFlameExtinguished(false);
        player.sendMessage("§aYour flame level and progress have been fully reset.");
        return true;
    }
}
