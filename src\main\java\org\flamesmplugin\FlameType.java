//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

package org.flamesmplugin;

import org.bukkit.Material;
import org.bukkit.Particle;

public enum FlameType {
    BURNING("[FIRE] Burning", Material.FIRE_CHARGE, Particle.FLAME, "Fire resistance. Strength while on fire."),
    FROST("[ICE] Frost", Material.ICE, Particle.SNOWFLAKE, "Immune to freezing. Regeneration in snow. Chance to freeze on hit."),
    EARTH("[EARTH] Earth", Material.STONE, Particle.BLOCK, "Double ore drops. Haste. Efficiency bonus."),
    AQUATIC("[WATER] Aquatic", Material.WATER_BUCKET, Particle.SPLASH, "Aqua affinity. Water breathing. Dolphin's grace."),
    GUST("[WIND] Gust", Material.FEATHER, Particle.CLOUD, "Speed. Extra hearts above Y70. No fall damage."),
    COSMIC("[STAR] Cosmic", Material.NETHER_STAR, Particle.END_ROD, "Resistance. Protection. Speed at night."),
    SHADOW("[SHADOW] Shadow", Material.WITHER_SKELETON_SKULL, Particle.CAMPFIRE_SIGNAL_SMOKE, "Invisibility. Chance to blind nearby players when crouching."),
    LIFE("[LIFE] Life", Material.GOLDEN_APPLE, Particle.HEART, "Extra hearts. Regeneration after eating golden apple."),
    DRAGON("[DRAGON] Dragon", Material.DRAGON_HEAD, Particle.DRAGON_BREATH, "Strength, speed, resistance, fire resistance. Immune to hostile mobs.");

    private final String displayName;
    private final Material iconMaterial;
    private final Particle particle;
    private final String description;

    private FlameType(String displayName, Material iconMaterial, Particle particle, String description) {
        this.displayName = displayName;
        this.iconMaterial = iconMaterial;
        this.particle = particle;
        this.description = description;
    }

    public String getDisplayName() {
        return this.displayName;
    }

    public Material getIconMaterial() {
        return this.iconMaterial;
    }

    public Particle getParticle() {
        return this.particle;
    }

    public String getDescription() {
        return this.description;
    }

    public static FlameType getRandomFlame() {
        FlameType[] values = values();
        FlameType[] nonDragon = new FlameType[values.length - 1];
        System.arraycopy(values, 0, nonDragon, 0, values.length - 1);
        return nonDragon[(int)(Math.random() * (double)nonDragon.length)];
    }

    public boolean isRandomlyAssignable() {
        return this != DRAGON;
    }
}
