//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.flamesmplugin;
import org.bukkit.enchantments.Enchantment;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.World;
import org.bukkit.attribute.Attribute;
import org.bukkit.attribute.AttributeInstance;
import org.bukkit.block.Block;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Fireball;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Monster;
import org.bukkit.entity.Player;
import org.bukkit.entity.Snowball;
import org.bukkit.event.player.PlayerTeleportEvent.TeleportCause;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.Vector;

public class FlameAbilities {
// import org.bukkit.enchantments.Enchantment; // Already imported at the top if needed
    // For Burning Enhancement: track which players have the effect and their original enchants
    private final java.util.Map<java.util.UUID, java.util.Map<Enchantment, Integer>> burningEnhancementTargets = new java.util.HashMap<>();
    private final java.util.Set<java.util.UUID> burningEnhancementActive = new java.util.HashSet<>();
    private final Map<UUID, Long> effectStartTimes = new HashMap<>();
    private final FlameSMPlugin plugin;
    private final FlameManager flameManager;
    private final CooldownManager cooldownManager;
    private final Map<UUID, Snowball> frostActiveSnowballs = new HashMap<>();
    public static double BURNING_DAMAGE = (double)120.0F;
    public static double FROST_DAMAGE = (double)120.0F;
    public static double EARTH_DAMAGE = (double)120.0F;
    public static double AQUATIC_DAMAGE = (double)120.0F;
    public static double GUST_DAMAGE = (double)120.0F;
    public static double COSMIC_DAMAGE = (double)120.0F;
    public static double SHADOW_DAMAGE = (double)120.0F;
    public static double LIFE_DAMAGE = (double)120.0F;
    public static double DRAGON_DAMAGE = (double)120.0F;

    public void useDragonFireball(Player player) {
        Location eye = player.getEyeLocation();
        World world = player.getWorld();
        Fireball fireball = (Fireball)world.spawn(eye.add(eye.getDirection().multiply(1.5)), Fireball.class);
        fireball.setShooter(player);
        fireball.setDirection(eye.getDirection());
        fireball.setYield(2.5F);
        fireball.setIsIncendiary(true);
        world.playSound(player.getLocation(), Sound.ENTITY_ENDER_DRAGON_SHOOT, 1.2F, 1.1F);
        ParticleUtils.createExplosion(player.getLocation().add(0.0, 1.0, 0.0), Particle.DRAGON_BREATH, 30);
        ParticleUtils.createSpiral(player.getLocation().add(0.0, 1.0, 0.0), Particle.FLAME, 2.0, 1.0);
        MessageUtils.sendAbilityActivation(player, "Dragon Fireball (F)", NamedTextColor.DARK_PURPLE);
    }

    public void useDragonBreath(final Player player) {
        MessageUtils.sendAbilityActivation(player, "Ender Ring", NamedTextColor.DARK_PURPLE);
        Location center = player.getLocation();
        final World world = center.getWorld();
        if (world != null) {
            world.playSound(center, Sound.ENTITY_ENDER_DRAGON_GROWL, 1.5F, 1.0F);
            ParticleUtils.createCircle(center, Particle.PORTAL, (double)10.0F, 50);
            ParticleUtils.createCircle(center.clone().add((double)0.0F, (double)1.0F, (double)0.0F), Particle.PORTAL, (double)10.0F, 50);
            (new BukkitRunnable() {
                int ticks = 0;

                public void run() {
                    if (this.ticks < 300 && player.isOnline()) {
                        Location ringCenter = player.getLocation();
                        ParticleUtils.createCircle(ringCenter, Particle.PORTAL, (double)10.0F, 50);
                        ParticleUtils.createCircle(ringCenter.clone().add((double)0.0F, (double)1.0F, (double)0.0F), Particle.PORTAL, (double)10.0F, 50);

                        for(Entity entity : world.getNearbyEntities(ringCenter, (double)10.0F, (double)10.0F, (double)10.0F)) {
                            if (entity instanceof LivingEntity && entity != player) {
                                LivingEntity target = (LivingEntity)entity;
                                target.damage((double)3.0F, player);
                                target.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 40, 0, false, false));
                                target.addPotionEffect(new PotionEffect(PotionEffectType.BLINDNESS, 40, 0, false, false));
                            }
                        }

                        this.ticks += 20;
                    } else {
                        this.cancel();
                    }
                }
            }).runTaskTimer(this.plugin, 0L, 20L);
        }
    }

    public FlameAbilities(FlameSMPlugin plugin, FlameManager flameManager, CooldownManager cooldownManager) {
        this.plugin = plugin;
        this.flameManager = flameManager;
        this.cooldownManager = cooldownManager;
    }

    public void handleCombatEffect(Player attacker, LivingEntity target, double damage) {
        FlamePlayer flamePlayer = this.flameManager.getFlamePlayer(attacker);
        if (flamePlayer != null && flamePlayer.hasActiveFlame()) {
            FlameType flameType = flamePlayer.getFlameType();
            java.util.UUID uuid = attacker.getUniqueId();
            // Burning Enhancement: one-time fire burst
            if (burningEnhancementActive.contains(uuid)) {
                // Prevent infinite damage recursion: tag entity before damaging
                if (!target.hasMetadata("burningEnhancementDamage")) {
                    try {
                        target.setFireTicks(40);
                        target.getWorld().spawnParticle(Particle.FLAME, target.getLocation().add(0,1,0), 8, 0.2, 0.2, 0.2, 0.02);
                        target.getWorld().playSound(target.getLocation(), Sound.ITEM_FIRECHARGE_USE, 1.1F, 1.1F);
                    } catch (Exception e) {
                        attacker.sendMessage("§c[Burning Enhancement] Error applying visual effects: " + e.getClass().getSimpleName());
                    }
                    // Deal 40% more damage (true bonus, not just fire), but clamp to avoid overkill
                    double bonus = Math.max(1.0, Math.min(6.0, damage * 0.4));
                    try {
                        target.setMetadata("burningEnhancementDamage", new org.bukkit.metadata.FixedMetadataValue(this.plugin, true));
                        target.damage(bonus, attacker);
                        target.removeMetadata("burningEnhancementDamage", this.plugin);
                    } catch (Exception e) {
                        attacker.sendMessage("§c[Burning Enhancement] Error applying bonus damage: " + e.getClass().getSimpleName());
                        try { target.removeMetadata("burningEnhancementDamage", this.plugin); } catch (Exception ignore) {}
                    }
                    attacker.sendMessage("§6Burning Enhancement: kaboom they blow up (extra damage!)");
                    // Remove the enhancement so it only triggers once
                    burningEnhancementActive.remove(uuid);
                }
            }
            // Passive burning effect
            if (flameType == FlameType.BURNING && target.getFireTicks() > 0) {
                // Prevent recursion/crash: only apply extra damage if not already caused by this effect
                // Use metadata to mark the entity as being processed for burning passive
                if (!(attacker.equals(target))) {
                    try {
                        if (!target.hasMetadata("burningPassive")) {
                            target.setMetadata("burningPassive", new org.bukkit.metadata.FixedMetadataValue(this.plugin, true));
                            target.damage(damage * 0.5, attacker);
                            target.removeMetadata("burningPassive", this.plugin);
                        }
                    } catch (StackOverflowError | Exception e) {
                        attacker.sendMessage("§c[Burning Passive] Error applying extra fire damage: " + e.getClass().getSimpleName());
                        try { target.removeMetadata("burningPassive", this.plugin); } catch (Exception ignore) {}
                    }
                }
            }
        }

    }

    public void applyPassiveAbilities(Player player) {
        FlamePlayer flamePlayer = this.flameManager.getFlamePlayer(player);

        if (flamePlayer.hasActiveFlame()) {
            FlameType flameType = flamePlayer.getFlameType();
            long now = System.currentTimeMillis();
            long effectDuration = 5000L;
            boolean spawnParticles = false;
            Long start = (Long)this.effectStartTimes.get(player.getUniqueId());
            // Always show passive particles for Gust
            if (flameType == FlameType.GUST) {
                spawnParticles = true;
            } else {
                if (start != null && now - start <= effectDuration) {
                    spawnParticles = true;
                } else {
                    this.effectStartTimes.put(player.getUniqueId(), now);
                    spawnParticles = true;
                }
            }

            switch (flameType) {
                case BURNING -> this.applyBurningPassive(player, spawnParticles);
                case FROST -> this.applyFrostPassive(player, spawnParticles);
                case EARTH -> this.applyEarthPassive(player, spawnParticles);
                case AQUATIC -> this.applyAquaticPassive(player, spawnParticles);
                case GUST -> this.applyGustPassive(player, spawnParticles);
                case COSMIC -> this.applyCosmicPassive(player, spawnParticles);
                case SHADOW -> this.applyShadowPassive(player, spawnParticles);
                case LIFE -> this.applyLifePassive(player, spawnParticles);
                case DRAGON -> this.applyDragonPassive(player, spawnParticles);
            }
        }
    }

    private void applyDragonPassive(Player player, boolean spawnParticles) {
        player.addPotionEffect(new PotionEffect(PotionEffectType.FIRE_RESISTANCE, 100, 1, true, false));
        player.addPotionEffect(new PotionEffect(PotionEffectType.SLOW_FALLING, 100, 0, true, false));
        if (spawnParticles) {
            ParticleUtils.createCircle(player.getLocation().add((double)0.0F, (double)1.0F, (double)0.0F), Particle.DRAGON_BREATH, (double)1.5F, 24);
            ParticleUtils.createSpiral(player.getLocation().add((double)0.0F, (double)0.5F, (double)0.0F), Particle.PORTAL, (double)2.0F, 0.7);
        }

    }

    public boolean useActiveAbility(Player player) {
        FlamePlayer flamePlayer = this.flameManager.getFlamePlayer(player);
        if (!flamePlayer.canUseLevel2Abilities()) {
            MessageUtils.sendUpgradeRequired(player);
            return false;
        } else if (this.cooldownManager.isOnCooldown(player)) {
            int remaining = this.cooldownManager.getRemainingCooldown(player);
            MessageUtils.sendCooldownMessage(player, remaining);
            return false;
        } else {
            FlameType flameType = flamePlayer.getFlameType();
            boolean success = false;
            switch (flameType) {
                case BURNING -> success = this.useBurningActive(player);
                case FROST -> success = this.useFrostActive(player);
                case EARTH -> success = this.useEarthActive(player);
                case AQUATIC -> success = this.useAquaticActive(player);
                case GUST -> success = this.useGustActive(player);
                case COSMIC -> success = this.useCosmicActive(player);
                case SHADOW -> success = this.useShadowActive(player);
                case LIFE -> success = this.useLifeActive(player);
                case DRAGON -> success = this.useDragonActive(player);
            }

            if (success) {
                int cooldown = CooldownManager.getCooldownForFlame(flameType);
                if (cooldown > 0) {
                    this.cooldownManager.setCooldown(player, cooldown);
                }
            }

            return success;
        }
    }

    private void applyBurningPassive(Player player, boolean spawnParticles) {
        player.addPotionEffect(new PotionEffect(PotionEffectType.FIRE_RESISTANCE, 100, 0, true, false));
        if (player.getFireTicks() > 0) {
            player.addPotionEffect(new PotionEffect(PotionEffectType.STRENGTH, 100, 0, true, false));
            if (spawnParticles) {
                ParticleUtils.createExplosion(player.getLocation().add((double)0.0F, (double)1.0F, (double)0.0F), Particle.FLAME, 20);
                ParticleUtils.createCircle(player.getLocation().add((double)0.0F, 1.2, (double)0.0F), Particle.LAVA, 1.1, 16);
                ParticleUtils.createSpiral(player.getLocation().add((double)0.0F, (double)0.5F, (double)0.0F), Particle.SMOKE, (double)2.0F, 0.7);
            }
        }

    }

    private void applyFrostPassive(Player player, boolean spawnParticles) {
        player.setFreezeTicks(0);
        Block blockAt = player.getLocation().getBlock();
        if (blockAt.getType() == Material.SNOW || blockAt.getType() == Material.SNOW_BLOCK || blockAt.getType() == Material.POWDER_SNOW) {
            player.addPotionEffect(new PotionEffect(PotionEffectType.REGENERATION, 100, 1, true, false));
            if (spawnParticles) {
                ParticleUtils.createCircle(player.getLocation().add((double)0.0F, (double)1.0F, (double)0.0F), Particle.SNOWFLAKE, 1.2, 24);
                ParticleUtils.createSpiral(player.getLocation().add((double)0.0F, (double)0.5F, (double)0.0F), Particle.SNOWFLAKE, (double)2.0F, 0.7);
            }
        }

    }

    private void applyEarthPassive(Player player, boolean spawnParticles) {
        player.addPotionEffect(new PotionEffect(PotionEffectType.HASTE, 100, 0, true, false));
        if (spawnParticles) {
            ParticleUtils.createCircle(player.getLocation().add((double)0.0F, (double)1.0F, (double)0.0F), Particle.FALLING_DUST, 1.2, 18, Material.DIRT);
            ParticleUtils.createSpiral(player.getLocation().add((double)0.0F, 0.2, (double)0.0F), Particle.CLOUD, (double)1.5F, (double)0.5F);
        }

    }

    private void applyAquaticPassive(Player player, boolean spawnParticles) {
        if (player.isInWater()) {
            player.addPotionEffect(new PotionEffect(PotionEffectType.DOLPHINS_GRACE, 100, 0, true, false));
            if (player.getRemainingAir() < player.getMaximumAir()) {
                player.setRemainingAir(Math.min(player.getMaximumAir(), player.getRemainingAir() + 20));
            }

            if (spawnParticles) {
                ParticleUtils.createCircle(player.getLocation().add((double)0.0F, (double)1.0F, (double)0.0F), Particle.BUBBLE, 1.2, 20);
                ParticleUtils.createSpiral(player.getLocation().add((double)0.0F, (double)0.5F, (double)0.0F), Particle.BUBBLE, (double)2.0F, 0.7);
            }
        }

    }

    private void applyGustPassive(Player player, boolean spawnParticles) {
        player.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, 100, 0, true, false));
        double y = player.getLocation().getY();
        int extraHearts = (int)Math.floor((y - (double)70.0F) / (double)10.0F) / 2;
        extraHearts = Math.max(0, Math.min(extraHearts, 10));
        AttributeInstance healthAttr = player.getAttribute(Attribute.GENERIC_MAX_HEALTH);
        if (healthAttr != null) {
            double baseHealth = (double)20.0F;
            double newMaxHealth = baseHealth + (double)extraHearts;
            if (healthAttr.getBaseValue() != newMaxHealth) {
                healthAttr.setBaseValue(newMaxHealth);
                if (player.getHealth() > newMaxHealth) {
                    player.setHealth(newMaxHealth);
                }
            }
        }

        if (y > (double)90.0F) {
            player.setAbsorptionAmount((double)4.0F);
        } else {
            player.setAbsorptionAmount((double)0.0F);
        }

        // Only a subtle passive effect (optional):
        if (spawnParticles) {
            Location base = player.getLocation().add(0.0, 1.0, 0.0);
            // Small, subtle cloud ring (not a tornado)
            double ringRadius = 0.7;
            double ringAngleOffset = (System.currentTimeMillis() / 100.0);
            for (double angle = 0.0; angle < Math.PI * 2; angle += Math.PI / 8D) {
                double x = Math.cos(angle + ringAngleOffset) * ringRadius;
                double z = Math.sin(angle + ringAngleOffset) * ringRadius;
                Location ringLoc = base.clone().add(x, 0, z);
                player.getWorld().spawnParticle(Particle.CLOUD, ringLoc, 1, 0.03, 0.03, 0.03, 0.01);
            }
        }

    }

    private void applyCosmicPassive(Player player, boolean spawnParticles) {
        player.addPotionEffect(new PotionEffect(PotionEffectType.RESISTANCE, 100, 0, true, false));
        // Auto-enchant all equipped armor with Protection I for Cosmic Flame
        ItemStack[] armor = player.getInventory().getArmorContents();
        for (ItemStack piece : armor) {
            if (piece != null && piece.getType() != Material.AIR) {
                this.plugin.getFlameItems().autoEnchantTool(piece, org.flamesmplugin.FlameType.COSMIC);
            }
        }
        World world = player.getWorld();
        long time = world.getTime();
        if (time > 13000L && time < 23000L) {
            player.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, 100, 0, true, false));
            if (spawnParticles) {
                ParticleUtils.createStar(player.getLocation().add((double)0.0F, (double)2.0F, (double)0.0F), Particle.END_ROD, (double)1.5F);
                ParticleUtils.createCircle(player.getLocation().add((double)0.0F, 2.2, (double)0.0F), Particle.PORTAL, 1.7, 24);
            }
        }

    }

    private void applyShadowPassive(Player player, boolean spawnParticles) {
        player.addPotionEffect(new PotionEffect(PotionEffectType.INVISIBILITY, 100, 0, true, false));
    }

    private void applyLifePassive(Player player, boolean spawnParticles) {
        AttributeInstance healthAttr = player.getAttribute(Attribute.GENERIC_MAX_HEALTH);
        if (healthAttr != null && healthAttr.getBaseValue() < (double)30.0F) {
            healthAttr.setBaseValue((double)30.0F);
        }

    }

    private boolean useBurningActive(Player player) {
        MessageUtils.sendAbilityActivation(player, "Burning Enhancement", NamedTextColor.GOLD);
        Location loc = player.getLocation().add(0.0, 1.0, 0.0);
        // Divine firestorm: multi-layered, multi-color, multi-sound
        player.getWorld().spawnParticle(Particle.FLAME, loc, 40, 0.7, 0.7, 0.7, 0.08);
        player.getWorld().spawnParticle(Particle.LAVA, loc, 24, 0.5, 0.5, 0.5, 0.05);
        player.getWorld().spawnParticle(Particle.SMOKE, loc, 18, 0.6, 0.6, 0.6, 0.04);
        player.getWorld().spawnParticle(Particle.CRIT, loc, 16, 0.5, 0.5, 0.5, 0.03);
        ParticleUtils.createExplosion(loc, Particle.FLAME, 24);
        ParticleUtils.createExplosion(loc, Particle.LAVA, 12);
        ParticleUtils.createCircle(loc, Particle.FLAME, 2.2, 24);
        ParticleUtils.createCircle(loc, Particle.LAVA, 1.7, 16);
        ParticleUtils.createSpiral(loc, Particle.FLAME, 2.5, 1.0);
        player.getWorld().playSound(loc, Sound.ENTITY_BLAZE_DEATH, 1.5F, 0.7F);
        player.getWorld().playSound(loc, Sound.ENTITY_GENERIC_EXPLODE, 1.7F, 0.8F);
        player.getWorld().playSound(loc, Sound.ITEM_FIRECHARGE_USE, 1.3F, 1.1F);
        player.getWorld().playSound(loc, Sound.BLOCK_LAVA_POP, 1.2F, 1.2F);

        // Store original main hand item enchants
        org.bukkit.inventory.ItemStack item = player.getInventory().getItemInMainHand();
        if (item == null || item.getType() == org.bukkit.Material.AIR) {
            player.sendMessage("§cYou must be holding a weapon to use Burning Enhancement!");
            return false;
        }
        // Only allow swords, axes, or maces
        String typeName = item.getType().name();
        if (!(typeName.endsWith("_SWORD") || typeName.endsWith("_AXE") || typeName.equals("MACE"))) {
            player.sendMessage("§cBurning Enhancement can only be used on swords, axes, or maces!");
            return false;
        }
        java.util.Map<org.bukkit.enchantments.Enchantment, Integer> originalEnchants = new java.util.HashMap<>(item.getEnchantments());

        // Add Fire Aspect II and Sharpness I
        item.addUnsafeEnchantment(org.bukkit.enchantments.Enchantment.FIRE_ASPECT, 2);
        item.addUnsafeEnchantment(Enchantment.SHARPNESS, 1);

        // Set up a one-time fire burst effect for the next attack
        java.util.UUID uuid = player.getUniqueId();
        this.burningEnhancementTargets.put(uuid, originalEnchants);
        this.burningEnhancementActive.add(uuid);

        // Schedule removal of the enhancement and restoration of original enchants
        new org.bukkit.scheduler.BukkitRunnable() {
            public void run() {
                // Restore original enchants
                org.bukkit.inventory.ItemStack item = player.getInventory().getItemInMainHand();
                item.getEnchantments().keySet().forEach(item::removeEnchantment);
                originalEnchants.forEach(item::addUnsafeEnchantment);
                burningEnhancementActive.remove(uuid);
                burningEnhancementTargets.remove(uuid);
                player.sendMessage("§6Burning Enhancement has faded. Your weapon has been restored.");
            }
        }.runTaskLater(this.plugin, 20 * 15); // 15 seconds duration

        return true;
    }

    private boolean useFrostActive(Player player) {
        MessageUtils.sendAbilityActivation(player, "Frost Nova", NamedTextColor.AQUA);
        Location loc = player.getLocation().add(0.0, 1.0, 0.0);
        // Divine frost nova: multi-layered, multi-sound, freezing shockwave
        player.getWorld().spawnParticle(Particle.SNOWFLAKE, loc, 80, 2.0, 2.0, 2.0, 0.15);
        player.getWorld().spawnParticle(Particle.CLOUD, loc, 40, 1.5, 1.5, 1.5, 0.10);
        player.getWorld().spawnParticle(Particle.END_ROD, loc, 24, 1.5, 1.5, 1.5, 0.07);
        player.getWorld().spawnParticle(Particle.CRIT, loc, 18, 1.2, 1.2, 1.2, 0.05);
        ParticleUtils.createExplosion(loc, Particle.SNOWFLAKE, 40);
        ParticleUtils.createExplosion(loc, Particle.END_ROD, 18);
        ParticleUtils.createCircle(loc, Particle.SNOWFLAKE, 2.0, 32);
        ParticleUtils.createCircle(loc, Particle.CLOUD, 1.5, 20);
        ParticleUtils.createSpiral(loc, Particle.SNOWFLAKE, 2.7, 1.0);
        player.getWorld().playSound(loc, Sound.BLOCK_GLASS_BREAK, 1.5F, 1.1F);
        player.getWorld().playSound(loc, Sound.BLOCK_SNOW_BREAK, 1.2F, 1.0F);
        player.getWorld().playSound(loc, Sound.BLOCK_AMETHYST_BLOCK_BREAK, 1.0F, 1.3F);

        for(Entity entity : player.getWorld().getNearbyEntities(loc, (double)6.0F, (double)6.0F, (double)6.0F)) {
            if (entity instanceof LivingEntity && entity != player) {
                ((LivingEntity)entity).damage(FROST_DAMAGE, player);
                ((LivingEntity)entity).addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 80, 2));
            }
        }

        return true;
    }

    private void createSnowExplosion(World world, Location loc, Player player) {
        world.spawnParticle(Particle.SNOWFLAKE, loc, 60, (double)1.0F, (double)1.0F, (double)1.0F, 0.2);
        world.spawnParticle(Particle.SNOWFLAKE, loc, 20, (double)0.5F, (double)0.5F, (double)0.5F, 0.1);
        world.playSound(loc, Sound.BLOCK_GLASS_BREAK, 1.2F, 1.5F);

        for(Entity entity : world.getNearbyEntities(loc, (double)2.5F, (double)2.5F, (double)2.5F)) {
            if (entity instanceof LivingEntity && entity != player) {
                ((LivingEntity)entity).damage(FROST_DAMAGE, player);
                ((LivingEntity)entity).addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 60, 1));
            }
        }

        UUID uuid = player.getUniqueId();
        if (this.frostActiveSnowballs.containsKey(uuid)) {
            this.frostActiveSnowballs.remove(uuid);
        }

    }

    public void triggerIceBallAbility(Player player) {
        // HARD BLOCK: Only allow Frost ability at level 2+
        FlamePlayer flamePlayer = this.flameManager.getFlamePlayer(player);
        if (flamePlayer == null || flamePlayer.getUpgradeLevel() < 2) {
            MessageUtils.sendAbilityActivation(player, "Frost ability unlocks at level 2!", NamedTextColor.AQUA);
            return;
        }
        UUID uuid = player.getUniqueId();
        if (this.frostActiveSnowballs.containsKey(uuid) && this.frostActiveSnowballs.get(uuid) != null && !((Snowball)this.frostActiveSnowballs.get(uuid)).isDead() && ((Snowball)this.frostActiveSnowballs.get(uuid)).isValid()) {
            MessageUtils.sendAbilityActivation(player, "Frost Barrage is still active! Wait for it to finish.", NamedTextColor.AQUA);
        } else {
            Location eyeLoc = player.getEyeLocation();
            Vector direction = eyeLoc.getDirection();
            Snowball main = this.shootIceBallTracked(player, eyeLoc, direction);
            this.shootIceBallTracked(player, eyeLoc, direction.clone().rotateAroundY(Math.toRadians((double)-10.0F)));
            this.shootIceBallTracked(player, eyeLoc, direction.clone().rotateAroundY(Math.toRadians((double)10.0F)));
            this.frostActiveSnowballs.put(uuid, main);
            player.getWorld().playSound(player.getLocation(), Sound.BLOCK_GLASS_BREAK, 1.2F, 1.5F);
            player.getWorld().playSound(player.getLocation(), Sound.BLOCK_SNOW_BREAK, 1.0F, 1.2F);
        }
    }

    private Snowball shootIceBallTracked(final Player player, Location start, Vector direction) {
        final World world = start.getWorld();
        if (world == null) {
            return null;
        } else {
            final Snowball snowball = (Snowball)world.spawn(start.add(direction.clone().multiply((double)1.5F)), Snowball.class);
            snowball.setShooter(player);
            snowball.setVelocity(direction.clone().multiply(1.1));
            snowball.customName(Component.text("Frost Barrage"));
            snowball.setCustomNameVisible(false);
            (new BukkitRunnable() {
                int ticks = 0;

                public void run() {
                    if (snowball.isValid() && !snowball.isDead() && this.ticks <= 40) {
                        for(double phi = (double)0.0F; phi < Math.PI; phi += (Math.PI / 8D)) {
                            for(double theta = (double)0.0F; theta < (Math.PI * 2D); theta += (Math.PI / 8D)) {
                                double x = Math.sin(phi) * Math.cos(theta);
                                double y = Math.sin(phi) * Math.sin(theta);
                                double z = Math.cos(phi);
                                world.spawnParticle(Particle.SNOWFLAKE, snowball.getLocation().add(x, y, z), 1, (double)0.0F, (double)0.0F, (double)0.0F, 0.01);
                            }
                        }

                        ++this.ticks;
                    } else {
                        FlameAbilities.this.createSnowExplosion(world, snowball.getLocation(), player);
                        snowball.remove();
                        FlameAbilities.this.frostActiveSnowballs.remove(player.getUniqueId());
                        this.cancel();
                    }
                }
            }).runTaskTimer(this.plugin, 0L, 1L);
            return snowball;
        }
    }

    private boolean useEarthActive(Player player) {
        MessageUtils.sendAbilityActivation(player, "Earth Shockwave", NamedTextColor.GREEN);
        final Location start = player.getLocation().clone();
        final World world = start.getWorld();
        if (world == null) return false;

        final Vector direction = player.getLocation().getDirection().setY(0).normalize();
        final int maxDistance = 20;
        // Removed unused variable 'step'
        final double width = 2.5;
        final int shockwaveTicks = 20; // 1 second duration

        // Divine opening: lightning, star, and block burst
        world.strikeLightningEffect(start);
        ParticleUtils.createStar(start, Particle.BLOCK, 2.5, Material.DIRT.createBlockData());
        ParticleUtils.createExplosion(start, Particle.EXPLOSION, 8);
        world.playSound(start, Sound.ENTITY_LIGHTNING_BOLT_THUNDER, 0.7F, 0.7F);
        world.spawnParticle(Particle.BLOCK, start, 16, 1.2, 0.5, 1.2, 0.1, Material.DIRT.createBlockData());
        world.spawnParticle(Particle.BLOCK, start, 12, 1.2, 0.5, 1.2, 0.1, Material.STONE.createBlockData());

        new BukkitRunnable() {
            int tick = 0;
            double distance = 0;
            @Override
            public void run() {
                if (tick > shockwaveTicks) {
                    this.cancel();
                    return;
                }
                distance = (maxDistance * tick) / (double)shockwaveTicks;
                Location waveLoc = start.clone().add(direction.clone().multiply(distance));
                waveLoc.setY(start.getY());

                // Visuals
                ParticleUtils.createCircle(waveLoc, Particle.BLOCK, width, 18, Material.STONE);
                ParticleUtils.createCircle(waveLoc, Particle.FALLING_DUST, width, 18, Material.DIRT);
                ParticleUtils.createSpiral(waveLoc, Particle.CLOUD, width, 0.7);
                ParticleUtils.createStar(waveLoc, Particle.BLOCK, 1.5, Material.DIRT.createBlockData());
                ParticleUtils.createExplosion(waveLoc, Particle.EXPLOSION, 6);
                world.spawnParticle(Particle.BLOCK, waveLoc, 8, 0.7, 0.3, 0.7, 0.05, Material.DIRT.createBlockData());
                world.spawnParticle(Particle.BLOCK, waveLoc, 6, 0.7, 0.3, 0.7, 0.05, Material.STONE.createBlockData());
                world.playSound(waveLoc, Sound.BLOCK_STONE_BREAK, 0.7F, 0.7F);
                world.playSound(waveLoc, Sound.BLOCK_ANVIL_LAND, 0.5F, 0.5F);

                // Affect entities at the leading edge
                for (Entity entity : world.getNearbyEntities(waveLoc, width, 2.0, width)) {
                    if (entity instanceof LivingEntity && entity != player) {
                        LivingEntity target = (LivingEntity) entity;
                        target.damage(EARTH_DAMAGE, player);
                        target.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 60, 3));
                        target.setVelocity(new Vector(0, 1.2, 0));
                        ParticleUtils.createExplosion(target.getLocation(), Particle.BLOCK, 12);
                        ParticleUtils.createStar(target.getLocation(), Particle.BLOCK, 1.0, Material.DIRT.createBlockData());
                        world.spawnParticle(Particle.BLOCK, target.getLocation(), 6, 0.5, 0.2, 0.5, 0.01, Material.DIRT.createBlockData());
                        if (target instanceof Player) {
                            MessageUtils.sendEffectNotification((Player) target, "Stunned by Earth Shockwave!", NamedTextColor.RED);
                        }
                    }
                }

                tick++;
            }
        }.runTaskTimer(this.plugin, 0L, 1L);
        return true;
    }

    private boolean useAquaticActive(final Player player) {
        MessageUtils.sendAbilityActivation(player, "Tidal Wave", NamedTextColor.BLUE);
        // Divine tidal wave: multi-layered, multi-sound, water vortex
        Location start = player.getLocation().add(0.0, 1.0, 0.0);
        player.getWorld().spawnParticle(Particle.SPLASH, start, 40, 1.5, 0.7, 1.5, 0.10);
        player.getWorld().spawnParticle(Particle.BUBBLE, start, 32, 1.2, 0.5, 1.2, 0.08);
        player.getWorld().spawnParticle(Particle.DOLPHIN, start, 12, 0.7, 0.2, 0.7, 0.05);
        player.getWorld().spawnParticle(Particle.END_ROD, start, 10, 0.5, 0.2, 0.5, 0.01);
        ParticleUtils.createExplosion(start, Particle.SPLASH, 18);
        ParticleUtils.createCircle(start, Particle.BUBBLE, 2.5, 24);
        ParticleUtils.createSpiral(start, Particle.BUBBLE, 2.5, 1.0);
        player.getWorld().playSound(start, Sound.ENTITY_DOLPHIN_SPLASH, 1.5F, 1.2F);
        player.getWorld().playSound(start, Sound.BLOCK_BUBBLE_COLUMN_UPWARDS_INSIDE, 1.2F, 1.2F);
        player.getWorld().playSound(start, Sound.ENTITY_PLAYER_SPLASH_HIGH_SPEED, 1.0F, 1.4F);
        (new BukkitRunnable() {
            int ticks = 0;

            public void run() {
                if (this.ticks < 140 && player.isOnline()) {
                    Vector direction = player.getEyeLocation().getDirection().setY(0).normalize();
                    if (direction.lengthSquared() == (double)0.0F) {
                        direction = new Vector(0, 0, 1);
                    }

                    double speed = 1.2;
                    Location currentLoc = player.getLocation().add(direction.clone().multiply(speed));
                    currentLoc.setY(player.getLocation().getY());
                    if (!currentLoc.getBlock().getType().isSolid() && !currentLoc.clone().add((double)0.0F, (double)1.0F, (double)0.0F).getBlock().getType().isSolid()) {
                        player.teleport(currentLoc, TeleportCause.PLUGIN);
                    }

                    ParticleUtils.createWave(currentLoc, direction, Particle.SPLASH, (double)3.0F, (double)2.0F);
                    ParticleUtils.createCircle(currentLoc, Particle.BUBBLE, (double)2.0F, 16);
                    ParticleUtils.createSpiral(currentLoc, Particle.BUBBLE, (double)2.0F, 0.7);
                    player.getWorld().spawnParticle(Particle.DOLPHIN, currentLoc, 8, 0.7, 0.2, 0.7, 0.05);
                    player.getWorld().spawnParticle(Particle.SPLASH, currentLoc, 18, 1.2, (double)0.5F, 1.2, 0.08);
                    player.getWorld().spawnParticle(Particle.CLOUD, currentLoc, 8, 0.7, 0.2, 0.7, 0.01);
                    player.getWorld().spawnParticle(Particle.END_ROD, currentLoc, 6, (double)0.5F, 0.2, (double)0.5F, 0.01);
                    World currentWorld = currentLoc.getWorld();
                    if (currentWorld != null) {
                        for(Entity entity : currentWorld.getNearbyEntities(currentLoc, (double)3.0F, (double)3.0F, (double)3.0F)) {
                            if (entity instanceof LivingEntity && entity != player) {
                                LivingEntity target = (LivingEntity)entity;
                                target.damage(FlameAbilities.AQUATIC_DAMAGE, player);
                                Vector knockback = entity.getLocation().toVector().subtract(currentLoc.toVector()).normalize().multiply(2);
                                entity.setVelocity(knockback);
                                currentWorld.spawnParticle(Particle.SPLASH, target.getLocation(), 12, (double)0.5F, (double)0.5F, (double)0.5F, 0.05);
                                currentWorld.spawnParticle(Particle.BUBBLE_POP, target.getLocation(), 8, 0.3, 0.3, 0.3, 0.01);
                            }
                        }
                    }

                    ++this.ticks;
                } else {
                    this.cancel();
                }
            }
        }).runTaskTimer(this.plugin, 0L, 1L);
        player.getWorld().playSound(player.getLocation(), Sound.ENTITY_DOLPHIN_SPLASH, 1.2F, 1.2F);
        player.getWorld().playSound(player.getLocation(), Sound.BLOCK_BUBBLE_COLUMN_UPWARDS_INSIDE, 1.0F, 1.2F);
        player.getWorld().playSound(player.getLocation(), Sound.ENTITY_PLAYER_SPLASH_HIGH_SPEED, 0.8F, 1.4F);
        return true;
    }

    private boolean useGustActive(final Player player) {
        MessageUtils.sendAbilityActivation(player, "Tornado Slam", NamedTextColor.AQUA);
        player.sendMessage("§b§l[Gust Flame] §7You summon a divine tornado!");
        player.setVelocity(new Vector(0.0, 2.8, 0.0));
        World world = player.getWorld();
        Location startLoc = player.getLocation();
        // Divine tornado: multi-layered, multi-sound, crit swirl
        world.playSound(startLoc, Sound.ENTITY_PHANTOM_FLAP, 2.2F, 1.3F);
        world.playSound(startLoc, Sound.ENTITY_GENERIC_EXPLODE, 2.0F, 0.6F);
        ParticleUtils.createExplosion(startLoc, Particle.CLOUD, 24);
        ParticleUtils.createCircle(startLoc.add(0,1,0), Particle.CLOUD, 3.5, 32);
        ParticleUtils.createSpiral(startLoc.add(0,1,0), Particle.CRIT, 2.5, 1.0);
        BukkitRunnable gustRunnable = new BukkitRunnable() {
            int phase = 0;
            BukkitRunnable tornadoTask = null;

            public void run() {
                if (!player.isOnline()) {
                    if (this.tornadoTask != null) {
                        this.tornadoTask.cancel();
                    }
                    this.cancel();
                } else {
                    if (this.phase == 0 && player.getVelocity().getY() <= (double)0.0F) {
                        this.phase = 1;
                        player.setVelocity(new Vector(0, -4, 0));
                    } else if (this.phase == 1 && player.getLocation().subtract((double)0.0F, 0.1, (double)0.0F).getBlock().getType().isSolid()) {
                        // Raise the tornado 2 blocks above the player for visibility
                        final Location impactLoc = player.getLocation().clone().add(0, 2, 0);
                        final World world = impactLoc.getWorld();
                        if (world != null) {
                            world.playSound(impactLoc, Sound.ENTITY_GENERIC_EXPLODE, 2.5F, 0.45F);
                            // Dramatic, ultra-wide, multi-layered tornado of cloud particles
                            int tornadoLayers = 7;
                            double[] radii = {2.5, 3.5, 4.5, 5.5, 6.5, 7.5, 8.5};
                            double[] heights = {0, 1.5, 3.0, 4.5, 6.0, 7.5, 9.0};
                            int pointsPerLayer = 48;
                            for (int layer = 0; layer < tornadoLayers; layer++) {
                                double baseRadius = radii[layer];
                                double baseY = heights[layer];
                                for (int i = 0; i < pointsPerLayer; i++) {
                                    double angle = (2 * Math.PI / pointsPerLayer) * i + (System.currentTimeMillis() / 350.0) + layer * 0.7;
                                    double x = Math.cos(angle) * baseRadius;
                                    double z = Math.sin(angle) * baseRadius;
                                    double y = baseY + (i * 0.10);
                                    Location cloudLoc = impactLoc.clone().add(x, y, z);
                                    world.spawnParticle(Particle.CLOUD, cloudLoc, 14, 0.10, 0.10, 0.10, 0.01);
                                }
                            }

                            // Four spinning cloud rings around the tornado
                            for (int ring = 0; ring < 4; ring++) {
                                double ringRadius = 6.0 + ring * 1.2;
                                double ringY = 2.0 + ring * 2.0;
                                int ringPoints = 48;
                                double ringAngleOffset = (System.currentTimeMillis() / (300.0 + ring * 60.0)) + ring * Math.PI / 2;
                                for (int i = 0; i < ringPoints; i++) {
                                    double angle = (2 * Math.PI / ringPoints) * i + ringAngleOffset;
                                    double x = Math.cos(angle) * ringRadius;
                                    double z = Math.sin(angle) * ringRadius;
                                    Location ringLoc = impactLoc.clone().add(x, ringY, z);
                                    world.spawnParticle(Particle.CLOUD, ringLoc, 18, 0.13, 0.13, 0.13, 0.01);
                                }
                            }
                            this.tornadoTask = new BukkitRunnable() {
                                int tornadoTicks = 0;

                                public void run() {
                                    if (this.tornadoTicks > 60) {
                                        this.cancel();
                                    } else {
                                        int layers = 24;
                                        double maxY = 8.0;
                                        for (int layer = 0; layer < layers; layer++) {
                                            double tornadoY = maxY * layer / (layers - 1);
                                            double radius = 1.2 + tornadoY * 0.55 + Math.sin(this.tornadoTicks * 0.08 + layer) * 0.15;
                                            double angleOffset = (this.tornadoTicks * 0.22) + tornadoY * 2.2 + layer * Math.PI / 8;
                                            for (double angle = 0.0; angle < Math.PI * 2; angle += Math.PI / 14D) {
                                                double x = Math.cos(angle + angleOffset) * radius;
                                                double z = Math.sin(angle + angleOffset) * radius;
                                                Location particleLoc = impactLoc.clone().add(x, tornadoY, z);
                                                // Increased particle count for debug
                                                world.spawnParticle(Particle.CLOUD, particleLoc, 12, 0.07, 0.07, 0.07, 0.01);
                                            }
                                        }
                                        // Multiple circling cloud rings (horizontal)
                                        for (int ring = 0; ring < 3; ring++) {
                                            double ringRadius = 3.2 + ring * 0.7 + Math.sin(this.tornadoTicks * 0.13 + ring) * 0.5;
                                            double ringY = 2.2 + ring * 2.0 + Math.cos(this.tornadoTicks * 0.09 + ring) * 0.7;
                                            double ringAngleOffset = (this.tornadoTicks * 0.18) + ring * Math.PI / 6;
                                            for (double angle = 0.0; angle < Math.PI * 2; angle += Math.PI / 8D) {
                                                double x = Math.cos(angle + ringAngleOffset) * ringRadius;
                                                double z = Math.sin(angle + ringAngleOffset) * ringRadius;
                                                Location ringLoc = impactLoc.clone().add(x, ringY, z);
                                                world.spawnParticle(Particle.CLOUD, ringLoc, 18, 0.09, 0.09, 0.09, 0.01);
                                            }
                                        }
                                        // Add a vertical swirl of crit particles for extra drama
                                        for (int swirl = 0; swirl < 2; swirl++) {
                                            double swirlRadius = 1.5 + swirl * 0.5;
                                            for (double t = 0; t < Math.PI * 2; t += Math.PI / 18D) {
                                                double x = Math.cos(t + this.tornadoTicks * 0.15 + swirl) * swirlRadius;
                                                double y = 1.0 + t * 1.2;
                                                double z = Math.sin(t + this.tornadoTicks * 0.15 + swirl) * swirlRadius;
                                                Location swirlLoc = impactLoc.clone().add(x, y, z);
                                                world.spawnParticle(Particle.CRIT, swirlLoc, 4, 0.01, 0.01, 0.01, 0.01);
                                            }
                                        }
                                        world.playSound(impactLoc, Sound.ENTITY_PHANTOM_FLAP, 1.5F, 1.2F);
                                        ++this.tornadoTicks;
                                    }
                                }
                            };
                            this.tornadoTask.runTaskTimer(FlameAbilities.this.plugin, 0L, 2L);

                            for(Entity entity : world.getNearbyEntities(impactLoc, 6.0, 6.0, 6.0)) {
                                if (entity instanceof LivingEntity && !entity.getUniqueId().equals(player.getUniqueId())) {
                                    LivingEntity target = (LivingEntity)entity;
                                    target.damage(FlameAbilities.GUST_DAMAGE, player);
                                    target.setVelocity(new Vector(0.0, 1.5, 0.0));
                                }
                            }
                        }

                        if (this.tornadoTask != null) {
                            this.tornadoTask.cancel();
                        }
                        this.cancel();
                    }
                }
            }
        };
        gustRunnable.runTaskTimer(this.plugin, 10L, 1L);
        return true;
    }

    private boolean useCosmicActive(final Player player) {
        MessageUtils.sendAbilityActivation(player, "Cosmic Singularity", NamedTextColor.LIGHT_PURPLE);
        final Location center = player.getLocation();
        final World world = center.getWorld();
        if (world == null) {
            return false;
        } else {
            // Divine cosmic: multi-layered, multi-sound, entity pull
            ParticleUtils.createCircle(center, Particle.PORTAL, 5.0, 48);
            ParticleUtils.createStar(center.add(0.0, 1.0, 0.0), Particle.END_ROD, 3.0);
            ParticleUtils.createSpiral(center, Particle.SOUL_FIRE_FLAME, 3.0, 1.5);
            ParticleUtils.createCircle(center, Particle.ASH, 3.2, 40);
            ParticleUtils.createCircle(center, Particle.END_ROD, 2.7, 32);
            world.spawnParticle(Particle.GLOW, center, 24, 1.5, 0.7, 1.5, 0.10);
            world.playSound(center, Sound.BLOCK_BEACON_ACTIVATE, 1.7F, 1.2F);
            (new BukkitRunnable() {
                int ticks = 0;

                public void run() {
                    if (this.ticks > 40) {
                        ParticleUtils.createExplosion(center, Particle.END_ROD, 80);
                        ParticleUtils.createExplosion(center, Particle.SOUL_FIRE_FLAME, 40);
                        ParticleUtils.createCircle(center, Particle.PORTAL, (double)6.0F, 60);
                        ParticleUtils.createCircle(center, Particle.GLOW, (double)4.0F, 32);
                        ParticleUtils.createCircle(center, Particle.ASH, (double)3.5F, 24);
                        world.playSound(center, Sound.ENTITY_GENERIC_EXPLODE, 1.5F, 1.5F);

                        for(Entity entity : world.getNearbyEntities(center, 6.0, 6.0, 6.0)) {
                            if (entity instanceof LivingEntity && !entity.getUniqueId().equals(player.getUniqueId())) {
                                ((LivingEntity)entity).damage(FlameAbilities.COSMIC_DAMAGE, player);
                            }
                        }

                        this.cancel();
                    } else {
                        for(Entity entity : world.getNearbyEntities(center, (double)8.0F, (double)8.0F, (double)8.0F)) {
                            if (entity instanceof LivingEntity && entity != player) {
                                Vector pull = center.toVector().subtract(entity.getLocation().toVector()).normalize().multiply((double)0.5F);
                                entity.setVelocity(entity.getVelocity().add(pull));
                                world.spawnParticle(Particle.PORTAL, entity.getLocation(), 8, (double)0.5F, (double)0.5F, (double)0.5F, 0.1);
                            }
                        }

                        ParticleUtils.createStar(center.clone().add((double)0.0F, (double)1.0F, (double)0.0F), Particle.END_ROD, (double)2.5F);
                        ++this.ticks;
                    }
                }
            }).runTaskTimer(this.plugin, 0L, 2L);
            return true;
        }
    }

    private boolean useShadowActive(final Player player) {
        MessageUtils.sendAbilityActivation(player, "Shadow Form", NamedTextColor.DARK_GRAY);
        int durationTicks = 500; // 25 seconds
        player.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, durationTicks, 2, false, false));
        player.addPotionEffect(new PotionEffect(PotionEffectType.INVISIBILITY, durationTicks, 0, false, false));
        player.addPotionEffect(new PotionEffect(PotionEffectType.STRENGTH, durationTicks, 3, false, false)); // Strength 3
        player.addPotionEffect(new PotionEffect(PotionEffectType.RESISTANCE, durationTicks, 2, false, false)); // Resistance 2

        for(Player other : Bukkit.getOnlinePlayers()) {
            if (other != player) {
                other.hidePlayer(this.plugin, player);
            }
        }

        // Divine shadow: multi-layered, multi-sound, soul swirl
        Location loc = player.getLocation().add(0.0, 1.0, 0.0);
        player.getWorld().spawnParticle(Particle.CAMPFIRE_COSY_SMOKE, loc, 60, 1.2, 1.2, 1.2, 0.12);
        player.getWorld().spawnParticle(Particle.SOUL, loc, 28, 0.9, 0.3, 0.9, 0.07);
        player.getWorld().spawnParticle(Particle.ASH, loc, 20, 0.7, 0.3, 0.7, 0.03);
        player.getWorld().spawnParticle(Particle.CRIT, loc, 16, 0.5, 0.5, 0.5, 0.03);
        ParticleUtils.createSpiral(loc, Particle.SMOKE, 2.5, 1.0);
        ParticleUtils.createSpiral(loc, Particle.SOUL_FIRE_FLAME, 2.5, 1.0);
        ParticleUtils.createCircle(loc, Particle.CRIT, 1.5, 20);
        ParticleUtils.createCircle(loc, Particle.SOUL, 1.5, 16);
        player.getWorld().playSound(loc, Sound.ENTITY_ENDERMAN_TELEPORT, 1.5F, 0.5F);
        player.getWorld().playSound(loc, Sound.BLOCK_RESPAWN_ANCHOR_SET_SPAWN, 1.0F, 0.7F);
        player.getWorld().playSound(loc, Sound.ENTITY_PHANTOM_FLAP, 0.8F, 0.4F);

        (new BukkitRunnable() {
            public void run() {
                for(Player other : Bukkit.getOnlinePlayers()) {
                    if (other != player) {
                        other.showPlayer(FlameAbilities.this.plugin, player);
                    }
                }
                MessageUtils.sendActionBar(player, "Shadow form ended", NamedTextColor.DARK_GRAY);
            }
        }).runTaskLater(this.plugin, durationTicks);
        return true;
    }

    private boolean useLifeActive(final Player player) {
        MessageUtils.sendAbilityActivation(player, "Life Bubble", NamedTextColor.LIGHT_PURPLE);
        final Location bubbleCenter = player.getLocation().clone();
        final World world = bubbleCenter.getWorld();
        if (world == null) {
            return false;
        } else {
            // Divine life: multi-layered, multi-sound, heart and glow
            player.getWorld().playSound(bubbleCenter, Sound.BLOCK_BEACON_POWER_SELECT, 1.5F, 1.2F);
            player.getWorld().playSound(bubbleCenter, Sound.BLOCK_BEACON_ACTIVATE, 1.2F, 1.3F);
            ParticleUtils.createExplosion(bubbleCenter, Particle.HEART, 18);
            ParticleUtils.createCircle(bubbleCenter.clone().add(0.0, 1.0, 0.0), Particle.HEART, 6.0, 40);
            ParticleUtils.createSpiral(bubbleCenter.clone().add(0.0, 1.0, 0.0), Particle.HEART, 3.0, 1.0);
            ParticleUtils.createCircle(bubbleCenter.clone().add(0.0, 1.2, 0.0), Particle.HEART, 2.0, 18);
            ParticleUtils.createCircle(bubbleCenter.clone().add(0.0, 1.1, 0.0), Particle.COMPOSTER, 3.0, 24);
            ParticleUtils.createSpiral(bubbleCenter.clone().add(0.0, 1.1, 0.0), Particle.END_ROD, 2.0, 1.0);
            ParticleUtils.createCircle(bubbleCenter.clone().add(0.0, 1.3, 0.0), Particle.GLOW, 2.0, 16);
            (new BukkitRunnable() {
                int ticks = 0;

                public void run() {
                    if (this.ticks >= 200) {
                        this.cancel();
                    } else {
                        ParticleUtils.createCircle(bubbleCenter.clone().add((double)0.0F, (double)1.0F, (double)0.0F), Particle.HEART, (double)5.0F, 30);
                        ParticleUtils.createSpiral(bubbleCenter.clone().add((double)0.0F, (double)1.0F, (double)0.0F), Particle.HEART, (double)2.0F, 0.7);
                        ParticleUtils.createCircle(bubbleCenter.clone().add((double)0.0F, 1.2, (double)0.0F), Particle.HEART, (double)1.5F, 12);
                        ParticleUtils.createCircle(bubbleCenter.clone().add((double)0.0F, 1.1, (double)0.0F), Particle.COMPOSTER, 2.2, 18);
                        ParticleUtils.createSpiral(bubbleCenter.clone().add((double)0.0F, 1.1, (double)0.0F), Particle.END_ROD, (double)1.5F, 0.7);
                        ParticleUtils.createCircle(bubbleCenter.clone().add((double)0.0F, 1.3, (double)0.0F), Particle.GLOW, 1.2, 10);

                        for(Entity entity : world.getNearbyEntities(bubbleCenter, 5.0, 5.0, 5.0)) {
                            if (entity instanceof Player) {
                                Player target = (Player)entity;
                                // Heal all players in the bubble, including the caster
                                AttributeInstance healthAttr = target.getAttribute(Attribute.GENERIC_MAX_HEALTH);
                                double maxHealth = healthAttr != null ? healthAttr.getValue() : 20.0;
                                double newHealth = Math.min(maxHealth, target.getHealth() + 1.0);
                                target.setHealth(newHealth);
                                MessageUtils.sendEffectNotification(target, "Healed", NamedTextColor.LIGHT_PURPLE);
                            } else if (entity instanceof Monster) {
                                Monster monster = (Monster)entity;
                                double dmg = 120.0;
                                monster.damage(dmg, player);
                            }
                        }

                        this.ticks += 10;
                    }
                }
            }).runTaskTimer(this.plugin, 0L, 10L);
            return true;
        }
    }

    private boolean useDragonActive(final Player player) {
        MessageUtils.sendAbilityActivation(player, "Dragon's Fury", NamedTextColor.DARK_PURPLE);
        final Location loc = player.getEyeLocation();
        final World world = loc.getWorld();
        if (world == null) {
            return false;
        } else {
            ParticleUtils.createExplosion(loc, Particle.DRAGON_BREATH, 60);
            ParticleUtils.createExplosion(loc, Particle.SOUL_FIRE_FLAME, 30);
            ParticleUtils.createCircle(loc, Particle.DRAGON_BREATH, (double)2.5F, 32);
            ParticleUtils.createCircle(loc, Particle.SOUL_FIRE_FLAME, 2.2, 18);
            ParticleUtils.createSpiral(loc, Particle.PORTAL, (double)3.0F, 1.2);
            ParticleUtils.createSpiral(loc, Particle.END_ROD, (double)2.0F, 0.8);
            world.playSound(loc, Sound.ENTITY_ENDER_DRAGON_GROWL, 1.5F, 1.0F);
            world.playSound(loc, Sound.ENTITY_ENDER_DRAGON_FLAP, 1.0F, 1.2F);
            this.shootDragonBreath(player, loc, loc.getDirection());
            (new BukkitRunnable() {
                int ticks = 0;

                public void run() {
                    if (player.isOnline() && this.ticks <= 10) {
                        for(Entity entity : world.getNearbyEntities(player.getLocation(), 8.0, 4.0, 8.0)) {
                            if (entity instanceof LivingEntity && !entity.getUniqueId().equals(player.getUniqueId())) {
                                Vector toEntity = entity.getLocation().toVector().subtract(player.getLocation().toVector());
                                double angle = loc.getDirection().normalize().angle(toEntity.normalize());
                                if (angle < (Math.PI / 4D)) {
                                    entity.setVelocity(loc.getDirection().clone().multiply(1.5).setY(0.7));
                                    ((LivingEntity)entity).damage(6.0, player);
                                    world.spawnParticle(Particle.EXPLOSION, entity.getLocation(), 8, 0.2, 0.2, 0.2, 0.01);
                                    world.spawnParticle(Particle.SOUL_FIRE_FLAME, entity.getLocation(), 8, 0.2, 0.2, 0.2, 0.01);
                                }
                            }
                        }

                        ParticleUtils.createCircle(player.getLocation().add((double)0.0F, (double)1.0F, (double)0.0F), Particle.DRAGON_BREATH, (double)4.0F, 32);
                        ParticleUtils.createCircle(player.getLocation().add((double)0.0F, (double)1.0F, (double)0.0F), Particle.SOUL_FIRE_FLAME, (double)2.0F, 16);
                        ParticleUtils.createSpiral(player.getLocation().add((double)0.0F, (double)1.0F, (double)0.0F), Particle.PORTAL, (double)3.0F, 1.2);
                        ParticleUtils.createSpiral(player.getLocation().add((double)0.0F, (double)1.0F, (double)0.0F), Particle.END_ROD, (double)2.0F, 0.8);
                        world.playSound(player.getLocation(), Sound.ENTITY_ENDER_DRAGON_FLAP, 0.7F, 1.2F);
                        ++this.ticks;
                    } else {
                        this.cancel();
                    }
                }
            }).runTaskTimer(this.plugin, 0L, 2L);
            return true;
        }
    }

    private void shootDragonBreath(Player player, Location start, Vector direction) {
        World world = start.getWorld();
        if (world != null) {
            for(int i = 0; i < 12; ++i) {
                Location point = start.clone().add(direction.clone().multiply((double)i * 0.7));
                world.spawnParticle(Particle.DRAGON_BREATH, point, 10, 0.3, 0.2, 0.3, 0.01);
                world.spawnParticle(Particle.FLAME, point, 4, 0.1, 0.1, 0.1, 0.01);
                world.playSound(point, Sound.ENTITY_BLAZE_SHOOT, 0.2F, 1.2F);

                for(Entity entity : world.getNearbyEntities(point, 1.2, 1.2, 1.2)) {
                    if (entity instanceof LivingEntity && !entity.getUniqueId().equals(player.getUniqueId())) {
                        double dmg = 120.0;
                        ((LivingEntity)entity).damage(dmg, player);
                        ((LivingEntity)entity).setFireTicks(60);
                    }
                }
            }

        }
    }

    public static void setAllAbilityDamage(double value) {
        BURNING_DAMAGE = value;
        FROST_DAMAGE = value;
        EARTH_DAMAGE = value;
        AQUATIC_DAMAGE = value;
        GUST_DAMAGE = value;
        COSMIC_DAMAGE = value;
        SHADOW_DAMAGE = value;
        LIFE_DAMAGE = value;
        DRAGON_DAMAGE = value;
    }

    public static void setAbilityDamage(String ability, double value) {
        switch (ability.toLowerCase()) {
            case "burning":
            case "fire":
                BURNING_DAMAGE = value; break;
            case "frost":
            case "ice":
                FROST_DAMAGE = value; break;
            case "earth":
            case "stone":
                EARTH_DAMAGE = value; break;
            case "aquatic":
            case "water":
                AQUATIC_DAMAGE = value; break;
            case "gust":
            case "wind":
            case "air":
                GUST_DAMAGE = value; break;
            case "cosmic":
            case "void":
            case "space":
                COSMIC_DAMAGE = value; break;
            case "shadow":
            case "dark":
                SHADOW_DAMAGE = value; break;
            case "life":
            case "nature":
                LIFE_DAMAGE = value; break;
            case "dragon":
                DRAGON_DAMAGE = value; break;
        }

    }

    public static double getAbilityDamage(String ability) {
        switch (ability.toLowerCase()) {
            case "burning" -> {
                return BURNING_DAMAGE;
            }
            case "frost" -> {
                return FROST_DAMAGE;
            }
            case "earth" -> {
                return EARTH_DAMAGE;
            }
            case "aquatic" -> {
                return AQUATIC_DAMAGE;
            }
            case "gust" -> {
                return GUST_DAMAGE;
            }
            case "cosmic" -> {
                return COSMIC_DAMAGE;
            }
            case "shadow" -> {
                return SHADOW_DAMAGE;
            }
            case "life" -> {
                return LIFE_DAMAGE;
            }
            case "dragon" -> {
                return DRAGON_DAMAGE;
            }
            default -> {
                return (double)120.0F;
            }
        }
    }

    public void handleGoldenAppleEat(Player player) {
    }
}
