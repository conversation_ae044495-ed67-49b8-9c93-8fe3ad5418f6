//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.flamesmplugin.commands;

import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.flamesmplugin.CooldownManager;
import org.flamesmplugin.FlameManager;
import org.flamesmplugin.FlamePlayer;
import org.flamesmplugin.FlameSMPlugin;
import org.flamesmplugin.FlameType;

public class FunCommand implements CommandExecutor {
    private final CooldownManager cooldownManager;

    public FunCommand(CooldownManager cooldownManager) {
        this.cooldownManager = cooldownManager;
    }

    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player player)) {
            sender.sendMessage("Only players can use this command.");
            return true;
        } else if (!player.hasPermission("flame.admin")) {
            player.sendMessage("§cYou don't have permission to use this command!");
            return true;
        } else {
            FlameManager flameManager = ((FlameSMPlugin)FlameSMPlugin.getPlugin(FlameSMPlugin.class)).getFlameManager();
            FlamePlayer flamePlayer = flameManager.getFlamePlayer(player);
            FlameType flameType = flamePlayer.getFlameType();
            int cooldown = CooldownManager.getCooldownForFlame(flameType);
            this.cooldownManager.setCooldown(player, cooldown);
            player.sendMessage("§aYour flame ability cooldown has been restored to default (" + cooldown + "s)");
            return true;
        }
    }
}
